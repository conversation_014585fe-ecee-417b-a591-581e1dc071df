// Common types for dashboard components
export interface BaseSectionProps {
  className?: string
}

export interface Skill {
  name: string
  category: string
  icon: any
}

export interface LearningItem {
  name: string
  icon: any
}

export interface Project {
  name: string
  status: 'Active' | 'Planning'
  icon: any
}

// Animation variants
export const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05,
      delayChildren: 0.1
    }
  }
}

export const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4
    }
  }
}

export const staggerContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
      delayChildren: 0.1
    }
  }
}
