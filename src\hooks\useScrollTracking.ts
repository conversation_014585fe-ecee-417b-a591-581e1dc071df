import { useState, useEffect, useRef, useCallback } from 'react'

interface UseScrollTrackingOptions {
  threshold?: number
  rootMargin?: string
}

interface ScrollTrackingResult {
  activeIndex: number
  registerElement: (index: number, element: HTMLElement | null) => void
  unregisterElement: (index: number) => void
}

export function useScrollTracking(
  itemCount: number,
  options: UseScrollTrackingOptions = {}
): ScrollTrackingResult {
  const { threshold = 0.5, rootMargin = '-20% 0px -20% 0px' } = options
  
  const [activeIndex, setActiveIndex] = useState(0)
  const elementsRef = useRef<Map<number, HTMLElement>>(new Map())
  const observerRef = useRef<IntersectionObserver | null>(null)

  const registerElement = useCallback((index: number, element: HTMLElement | null) => {
    if (!element) return

    // Store the element
    elementsRef.current.set(index, element)

    // Observe the element
    if (observerRef.current) {
      observerRef.current.observe(element)
    }
  }, [])

  const unregisterElement = useCallback((index: number) => {
    const element = elementsRef.current.get(index)
    if (element && observerRef.current) {
      observerRef.current.unobserve(element)
      elementsRef.current.delete(index)
    }
  }, [])

  useEffect(() => {
    // Create intersection observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        // Find the entry with the highest intersection ratio
        let maxRatio = 0
        let maxIndex = 0

        entries.forEach((entry) => {
          if (entry.isIntersecting && entry.intersectionRatio > maxRatio) {
            // Find the index of this element
            for (const [index, element] of elementsRef.current.entries()) {
              if (element === entry.target) {
                maxRatio = entry.intersectionRatio
                maxIndex = index
                break
              }
            }
          }
        })

        // Update active index if we found an intersecting element
        if (maxRatio > 0) {
          setActiveIndex(maxIndex)
        }
      },
      {
        threshold,
        rootMargin,
      }
    )

    // Observe all currently registered elements
    elementsRef.current.forEach((element) => {
      if (observerRef.current) {
        observerRef.current.observe(element)
      }
    })

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [threshold, rootMargin])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
      elementsRef.current.clear()
    }
  }, [])

  return {
    activeIndex,
    registerElement,
    unregisterElement,
  }
}

// Additional hook for smooth scrolling to specific project
export function useScrollToProject() {
  const scrollToProject = useCallback((index: number, containerId: string = 'projects-container') => {
    const container = document.getElementById(containerId)
    const projectElement = document.getElementById(`project-${index}`)
    
    if (container && projectElement) {
      const containerRect = container.getBoundingClientRect()
      const projectRect = projectElement.getBoundingClientRect()
      
      // Calculate the scroll position to center the project in the viewport
      const scrollTop = container.scrollTop + projectRect.top - containerRect.top - (window.innerHeight / 2) + (projectRect.height / 2)
      
      container.scrollTo({
        top: scrollTop,
        behavior: 'smooth'
      })
    }
  }, [])

  return { scrollToProject }
}
