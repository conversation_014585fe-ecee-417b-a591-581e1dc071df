import React, { useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, useMotionValue, useSpring, Variants } from 'framer-motion';
import { scrollToTopInstant } from '@/components/ScrollToTop';
import { BaseSectionProps } from './types';
import { ArrowRight } from 'lucide-react';
import FloatingBadge from '@/components/FloatingBadge';

// Animation variants for staggered entrance
const containerVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1,
    },
  },
};

const itemVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

// A new, advanced button component where the entire button has a magnetic hover effect
const MagneticButton = ({ onClick }: { onClick: () => void }) => {
  const ref = useRef<HTMLButtonElement>(null);

  const x = useMotionValue(0);
  const y = useMotionValue(0);

  const buttonX = useSpring(x, { stiffness: 150, damping: 20, mass: 0.1 });
  const buttonY = useSpring(y, { stiffness: 150, damping: 20, mass: 0.1 });

  const handleMouseMove = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (!ref.current) return;
    const rect = ref.current.getBoundingClientRect();
    const width = rect.width;
    const height = rect.height;
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    
    const xPct = (mouseX / width - 0.5);
    const yPct = (mouseY / height - 0.5);
    
    x.set(xPct * width * 0.4);
    y.set(yPct * height * 0.4);
  };

  const handleMouseLeave = () => {
    x.set(0);
    y.set(0);
  };

  return (
    <motion.button
      ref={ref}
      onClick={onClick}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      style={{ x: buttonX, y: buttonY }}
      className="group relative flex items-center justify-center gap-x-4 rounded-full bg-neutral-800/80 hover:bg-white text-white hover:text-neutral-900 transition-all duration-300 ease-out pl-8 pr-3 py-3 shadow-[inset_0_0_15px_rgba(255,255,255,0.05)]"
    >
      <span className="text-lg font-medium transition-colors duration-300">
        Get In Touch
      </span>
      <div className="relative w-12 h-12 rounded-full bg-neutral-700/80 group-hover:bg-neutral-900 overflow-hidden transition-colors duration-300">
        <div className="absolute inset-0 flex items-center justify-center text-white group-hover:text-white">
          <ArrowRight className="w-5 h-5" />
        </div>
      </div>
    </motion.button>
  );
};


export default function CallToActionSection({ className }: BaseSectionProps) {
  const navigate = useNavigate();
  const sectionRef = useRef<HTMLElement>(null);

  const handleNavigation = (path: string) => {
    scrollToTopInstant();
    navigate(path);
  };

  return (
    <motion.section
      ref={sectionRef}
      className={`relative overflow-hidden pt-24 pb-12 lg:pt-40 lg:pb-24 ${className || ''}`}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.3 }}
      variants={containerVariants}
    >
      <div className="container relative mx-auto px-4 text-center z-10">
        
        <motion.div className="relative mb-8 flex justify-center" variants={itemVariants}>
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-28 h-28 bg-white/5 rounded-full blur-3xl" />
          <img
            src="/image/cj-transparent-white.png"
            alt="CJ Jutba Logo"
            className="relative w-16 h-16"
          />
        </motion.div>

        {/* ✨ Restructured the heading to absolutely position the badge */}
        <motion.div variants={itemVariants}>
            <h2 className="text-display text-4xl md:text-5xl lg:text-6xl text-foreground uppercase tracking-wider">
                <span className="font-normal">From Concept to</span>{' '}
                <span className="font-bold text-primary">
                    Creation
                </span>
            </h2>
            {/* This container is centered and acts as the relative parent for the badge */}
            <div className="relative inline-block mt-2">
                <h2 className="text-display text-4xl md:text-5xl lg:text-6xl text-foreground uppercase tracking-wider">
                    <span className="font-normal">Let's Make It</span>{' '}
                    <span className="font-bold text-primary">
                        Happen!
                    </span>
                </h2>
                {/* Badge is absolutely positioned relative to the heading container */}
                {/* ✨ Added mt-4 to move the badge down slightly */}
                <div className="absolute top-1/2 -translate-y-1/2 right-0 translate-x-full ml-4 mt-4 hidden lg:block">
                    <FloatingBadge dragConstraintsRef={sectionRef} />
                </div>
            </div>
        </motion.div>

        {/* ✨ Button is now in its own centered container */}
        <motion.div 
          className="mt-12 flex justify-center"
          variants={itemVariants}
        >
          <MagneticButton onClick={() => handleNavigation('/contact')} />
        </motion.div>

        <motion.div className="mt-16 max-w-2xl mx-auto" variants={itemVariants}>
          <p className="text-lg text-foreground font-bold mb-4">
            I'm available for full-time roles & freelance projects.
          </p>
          <p className="text-lg text-muted-foreground">
            I thrive on crafting dynamic web applications, and delivering seamless user experiences.
          </p>
        </motion.div>

      </div>
    </motion.section>
  );
}
