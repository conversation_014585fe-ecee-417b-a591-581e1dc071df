import React, { useRef, useState, useLayoutEffect } from 'react';
import { motion, useScroll, useTransform, useSpring, MotionValue } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { GraduationCap, Rocket, Code, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { scrollToTopInstant } from '@/components/ScrollToTop';
import { BaseSectionProps } from './types';
import { cn } from '@/lib/utils';

// Data for our journey steps for easier management
const journeySteps = [
 {
  icon: GraduationCap,
  title: "Foundations",
  subtitle: "Graduation - May 2025",
  description: "Built a strong technical foundation with a degree in Computer Engineering.",
  color: "text-purple-400",
  align: "left",
 },
 {
  icon: Code,
  title: "Ignition",
  subtitle: "Discovery",
  description: "Discovered a deep passion for bringing ideas to life on the web.",
  color: "text-pink-400",
  align: "right",
 },
 {
  icon: Rocket,
  title: "Launch",
  subtitle: "Developer - Present",
  description: "Crafting dynamic, user-centric applications and continuously pushing boundaries.",
  color: "text-orange-400",
  align: "left",
 },
];

// ✨ Reusable animated card now includes a scaling animation based on scroll progress
const JourneyStepCard = ({ 
 step, 
 index,
 scrollYProgress 
}: { 
 step: typeof journeySteps[0], 
 index: number,
 scrollYProgress: MotionValue<number> 
}) => {
 // Each card is active in a specific segment of the scroll progress.
 const start = index / journeySteps.length;
 const end = start + 1 / journeySteps.length;
 
 // Create a transform that maps scroll progress to a scale value for the card.
 const scale = useTransform(scrollYProgress, [start, (start + end) / 2, end], [1, 1.05, 1]);

 return (
  <motion.li
   initial={{ opacity: 0, x: step.align === 'left' ? -50 : 50 }}
   whileInView={{ opacity: 1, x: 0 }}
   viewport={{ once: true, amount: 0.5 }}
   transition={{ duration: 0.6, delay: index * 0.2 }}
   style={{ scale }}
   // FIX: Updated hover effect to be more subtle and smooth
   className={cn(
    "w-full md:w-5/12 p-6 rounded-2xl bg-neutral-900/60 backdrop-blur-lg border border-neutral-800/80 shadow-lg transition-all duration-300 hover:-translate-y-1 hover:shadow-xl hover:shadow-purple-500/10 hover:border-neutral-700/60",
    step.align === 'left' ? 'md:self-start' : 'md:self-end'
   )}
  >
   <div className="flex items-center gap-4 mb-3">
    <div className={cn("w-12 h-12 rounded-lg flex items-center justify-center bg-neutral-800/80 border border-neutral-700/50", step.color)}>
     <step.icon className="w-6 h-6" />
    </div>
    <div>
     <h3 className="text-xl font-bold text-foreground">{step.title}</h3>
     <p className="text-sm font-semibold text-muted-foreground">{step.subtitle}</p>
    </div>
   </div>
   <p className="text-neutral-400 text-sm leading-relaxed">{step.description}</p>
  </motion.li>
 );
};

// The main component
export default function JourneySummarySection({ className }: BaseSectionProps) {
 const navigate = useNavigate();
 const timelineRef = useRef<HTMLUListElement>(null);
 const pathRef = useRef<SVGPathElement>(null);
 const [pathLength, setPathLength] = useState(0);

 useLayoutEffect(() => {
  if (pathRef.current) {
   setPathLength(pathRef.current.getTotalLength());
  }
 }, []);

 const { scrollYProgress } = useScroll({
  target: timelineRef,
  offset: ["start center", "end center"]
 });

 // FIX: Smooth the scrollYProgress value with a spring for a seamless animation
 const smoothScrollYProgress = useSpring(scrollYProgress, {
  stiffness: 100,
  damping: 50,
  restDelta: 0.001
 });

 const avatarPathProgress = useTransform(smoothScrollYProgress, [0, 1], [0.03, 0.97], { clamp: true });

 const avatarPosition = useTransform(avatarPathProgress, (pos) => {
  if (pathRef.current && pathLength > 0) {
   const point = pathRef.current.getPointAtLength(pos * pathLength);
   return { x: point.x, y: point.y };
  }
  return { x: 100, y: 0 };
 });

 const avatarX = useTransform(avatarPosition, (pos) => pos.x - 24);
 const avatarY = useTransform(avatarPosition, (pos) => pos.y - 24);

 const maskHeight = useTransform(smoothScrollYProgress, [0, 1], [24, 776]);

 const handleNavigation = (path: string) => {
  scrollToTopInstant();
  navigate(path);
 };

 return (
  <section 
   className={cn("py-24 lg:py-40 relative", className)}
   aria-labelledby="journey-summary-heading"
  >
   <div className="container mx-auto px-4 text-center mb-20">
    <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
     MY JOURNEY
    </span>
    <motion.h2
     id="journey-summary-heading"
     initial={{ opacity: 0, y: 20 }}
     whileInView={{ opacity: 1, y: 0 }}
     viewport={{ once: true, amount: 0.5 }}
     transition={{ duration: 0.6, delay: 0.1 }}
     className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-foreground mt-2"
    >
     A Glimpse Into My{' '}
     <span className="relative font-apparel font-black italic -skew-x-12 inline-block pb-3 bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500 bg-[length:200%_200%] bg-clip-text text-transparent animate-gradient-shift">
      Story
     </span>
    </motion.h2>
   </div>

   <div className="container mx-auto px-4 max-w-4xl">
    <div className="relative">
     <div aria-hidden="true" className="absolute top-0 left-1/2 -translate-x-1/2 h-full w-full max-w-xs z-0">
      <svg width="100%" height="100%" viewBox="0 0 200 800" preserveAspectRatio="xMidYMin meet" className="overflow-visible">
       <defs>
        <linearGradient id="journey-summary-gradient" gradientTransform="rotate(90)">
         <stop offset="0%" stopColor="#a855f7" />
         <stop offset="50%" stopColor="#ec4899" />
         <stop offset="100%" stopColor="#f97316" />
        </linearGradient>
        <filter id="glow" x="-100%" y="-100%" width="300%" height="300%">
         <feGaussianBlur stdDeviation="12" result="coloredBlur"/>
         <feMerge>
          <feMergeNode in="coloredBlur"/>
          <feMergeNode in="SourceGraphic"/>
         </feMerge>
        </filter>
        <mask id="journey-mask">
         <motion.rect 
          x="0" 
          y="0" 
          width="200" 
          fill="white"
          style={{ height: maskHeight }} 
         />
        </mask>
       </defs>
       <path
        d="M 100,0 C 40,150 160,250 100,400 C 40,550 160,650 100,800"
        fill="none"
        stroke="#373737"
        strokeWidth="10"
        strokeLinecap="round"
       />
       <g mask="url(#journey-mask)">
        <path
         ref={pathRef}
         d="M 100,0 C 40,150 160,250 100,400 C 40,550 160,650 100,800"
         fill="none"
         stroke="url(#journey-summary-gradient)"
         strokeWidth="10"
         strokeLinecap="round"
         style={{ filter: 'url(#glow)' }}
        />
       </g>
       <motion.foreignObject
        x={avatarX}
        y={avatarY}
        width="48"
        height="48"
        initial={{ opacity: 0, scale: 0.5 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
       >
        <div className="w-full h-full">
         <img
          src="/image/profile.png"
          alt="CJ Jutba's avatar"
          className="w-full h-full rounded-full object-cover shadow-xl ring-2 ring-purple-500/30"
         />
        </div>
       </motion.foreignObject>
      </svg>
     </div>

     <motion.ul
      ref={timelineRef}
      className="relative flex flex-col items-center gap-20 list-none p-0"
     >
      {journeySteps.map((step, index) => (
        // Pass the smoothed scroll progress to the card
       <JourneyStepCard key={index} step={step} index={index} scrollYProgress={smoothScrollYProgress} />
      ))}
     </motion.ul>
    </div>
   </div>
   
   <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true, amount: 0.5 }}
    transition={{ duration: 0.6, delay: 0.5 }}
    className="text-center mt-24"
   >
    <Button
     onClick={() => handleNavigation('/about')}
     size="lg"
     variant="outline"
     className="group text-lg rounded-xl px-8 py-6 bg-neutral-900/50 border-neutral-700/80 hover:bg-neutral-800/50 hover:border-primary/50 transition-all duration-300"
    >
     Explore The Full Journey
     <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
    </Button>
   </motion.div>
  </section>
 );
}