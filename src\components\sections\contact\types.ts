import { LucideIcon } from "lucide-react"

export interface BaseSectionProps {
  className?: string
}

export interface ContactMethod {
  icon: LucideIcon
  label: string
  value: string
  href: string
  description: string
}

export interface SocialLink {
  icon: LucideIcon
  label: string
  href: string
  color: string
}

export interface ContactStat {
  label: string
  value: string
  icon: LucideIcon
}

export interface FormData {
  name: string
  email: string
  inquiryType: string
  subject: string
  message: string
}

export interface FormErrors {
  name?: string
  email?: string
  subject?: string
  message?: string
}

// Animation variants
export const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05,
      delayChildren: 0.1
    }
  }
}

export const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4
    }
  }
}

export const staggerContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
      delayChildren: 0.1
    }
  }
}
