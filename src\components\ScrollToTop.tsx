import { useEffect, useState } from 'react'
import { useLocation } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowUp } from 'lucide-react'

/**
 * Simple and reliable scroll to top function
 * Targets the main content area to preserve sidebar/navbar positioning
 */
const scrollToTopMain = () => {
  // First try to scroll the window (this works for most layouts)
  window.scrollTo({ top: 0, behavior: 'smooth' })

  // Also try to scroll the main element if it exists and is scrollable
  const mainElement = document.querySelector('main') as HTMLElement
  if (mainElement && mainElement.scrollTop > 0) {
    mainElement.scrollTo({ top: 0, behavior: 'smooth' })
  }
}



/**
 * Enhanced ScrollToTop component with smooth animations and visual feedback
 * Automatically scrolls to top on route changes with cinematic effects
 */
export default function ScrollToTop() {
  const { pathname } = useLocation()

  useEffect(() => {
    // Simple scroll to top on route change without indicators
    scrollToTopMain()
  }, [pathname])

  return null // No visual indicator needed
}

/**
 * Hook to programmatically scroll to top
 * Can be used in components for manual scroll-to-top functionality
 */
export const useScrollToTop = () => {
  const scrollToTop = () => {
    scrollToTopMain()
  }

  return scrollToTop
}

/**
 * Smooth scroll to top (for manual use)
 * Simple and reliable without indicators
 */
export const scrollToTopSmooth = () => {
  scrollToTopMain()
}

/**
 * Utility function to scroll to top instantly (no animation)
 * Useful for immediate page transitions and navigation
 */
export const scrollToTopInstant = () => {
  window.scrollTo(0, 0)
  const mainElement = document.querySelector('main') as HTMLElement
  if (mainElement && mainElement.scrollTop > 0) {
    mainElement.scrollTo(0, 0)
  }
}

/**
 * Enhanced navigation function that combines route navigation with scroll-to-top
 * Use this instead of navigate() directly for better UX
 */
export const navigateWithScrollToTop = (navigate: (path: string) => void, path: string) => {
  // First scroll to top instantly for immediate navigation
  scrollToTopInstant()
  // Then navigate
  navigate(path)
}

/**
 * Floating Scroll-to-Top Button Component
 * Appears when user scrolls down and provides smooth scroll-to-top functionality
 */
export const FloatingScrollToTop = () => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true)
      } else {
        setIsVisible(false)
      }
    }

    window.addEventListener('scroll', toggleVisibility)
    return () => window.removeEventListener('scroll', toggleVisibility)
  }, [])

  const handleScrollToTop = () => {
    scrollToTopSmooth()
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.button
          initial={{ opacity: 0, scale: 0.8, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 20 }}
          whileHover={{ scale: 1.1, y: -2 }}
          whileTap={{ scale: 0.95 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          onClick={handleScrollToTop}
          className="fixed bottom-8 right-8 z-50 bg-primary hover:bg-primary/90 text-primary-foreground p-4 rounded-full shadow-xl hover:shadow-2xl border border-primary/20 backdrop-blur-sm transition-all duration-300 group"
          aria-label="Scroll to top"
        >
          <ArrowUp className="w-6 h-6 group-hover:scale-110 transition-transform duration-200" />
        </motion.button>
      )}
    </AnimatePresence>
  )
}
