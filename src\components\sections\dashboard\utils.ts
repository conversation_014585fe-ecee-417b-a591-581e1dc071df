import React from 'react'

// Helper function to render icons safely
export const renderIcon = (IconComponent: any, className: string) => {
  if (typeof IconComponent === 'function') {
    return React.createElement(IconComponent, { className })
  }
  return null
}

// Navigation helper
export const createNavigationHandler = (navigate: (path: string) => void, scrollToTopInstant: () => void) => {
  return (path: string) => {
    scrollToTopInstant()
    navigate(path)
  }
}
