import {
  <PERSON>,
  Palette,
  Wrench,
  <PERSON>,
  <PERSON><PERSON>pen,
  Star,
  TrendingUp,
  Award,
  Target,
  Zap,
  Brain,
  Lightbulb,
  Rocket,
  Calendar,
  Clock,
  CheckCircle,
  Circle,
  Database,
  Globe,
  Smartphone,
  Monitor,
  GitBranch,
  Settings,
  Layers,
  PenTool,
  Coffee,
  Heart,
  Eye,
  MessageSquare,
  Users2,
  Timer,
  Focus,
  Repeat,
  <PERSON>rkles
} from "lucide-react";
import {
  SiReact,
  SiTypescript,
  SiNextdotjs,
  SiTailwindcss,
  SiJavascript,
  SiGit,
  SiHtml5,
  SiCss3,
  SiNodedotjs,
  SiPostgresql,
  SiPrisma,
  SiFigma,
  SiSass,
  SiFramer,
  SiSupabase,
  SiVercel,
  SiVite,
  SiVitest,
  SiDocker,
  SiGithub,

  SiExpress
} from "react-icons/si";

// Technical Skills Data
export const technicalSkills = [
  // Frontend Technologies
  {
    name: "React",
    level: 92,
    category: "Frontend",
    icon: SiReact,
    experience: "2+ years",
    description: "Advanced component architecture, hooks, context, and performance optimization",
    projects: 15,
    color: "#61DAFB"
  },
  {
    name: "TypeScript",
    level: 88,
    category: "Frontend",
    icon: SiTypescript,
    experience: "2+ years",
    description: "Strong typing, interfaces, generics, and advanced type manipulation",
    projects: 12,
    color: "#3178C6"
  },
  {
    name: "Next.js",
    level: 85,
    category: "Frontend",
    icon: SiNextdotjs,
    experience: "1+ years",
    description: "App Router, SSR, SSG, API routes, and performance optimization",
    projects: 8,
    color: "#000000"
  },
  {
    name: "JavaScript",
    level: 90,
    category: "Frontend",
    icon: SiJavascript,
    experience: "2+ years",
    description: "ES6+, async/await, closures, prototypes, and modern JavaScript patterns",
    projects: 15,
    color: "#F7DF1E"
  },
  {
    name: "HTML5",
    level: 95,
    category: "Frontend",
    icon: SiHtml5,
    experience: "2+ years",
    description: "Semantic markup, accessibility, SEO optimization, and modern HTML features",
    projects: 15,
    color: "#E34F26"
  },
  {
    name: "CSS3",
    level: 90,
    category: "Frontend",
    icon: SiCss3,
    experience: "2+ years",
    description: "Flexbox, Grid, animations, responsive design, and CSS architecture",
    projects: 15,
    color: "#1572B6"
  },

  // Styling & Design
  {
    name: "Tailwind CSS",
    level: 92,
    category: "Styling",
    icon: SiTailwindcss,
    experience: "2+ years",
    description: "Utility-first CSS, custom configurations, and responsive design systems",
    projects: 12,
    color: "#06B6D4"
  },
  {
    name: "Sass/SCSS",
    level: 80,
    category: "Styling",
    icon: SiSass,
    experience: "1+ years",
    description: "Variables, mixins, nesting, and modular CSS architecture",
    projects: 6,
    color: "#CC6699"
  },
  {
    name: "Framer Motion",
    level: 88,
    category: "Animation",
    icon: SiFramer,
    experience: "1+ years",
    description: "Complex animations, gestures, layout animations, and micro-interactions",
    projects: 10,
    color: "#0055FF"
  },

  // Backend Technologies
  {
    name: "Node.js",
    level: 85,
    category: "Backend",
    icon: SiNodedotjs,
    experience: "1+ years",
    description: "Server-side JavaScript, npm ecosystem, and asynchronous programming",
    projects: 8,
    color: "#339933"
  },
  {
    name: "Express.js",
    level: 82,
    category: "Backend",
    icon: SiExpress,
    experience: "1+ years",
    description: "RESTful APIs, middleware, authentication, and error handling",
    projects: 6,
    color: "#000000"
  },

  // Database & Tools
  {
    name: "PostgreSQL",
    level: 78,
    category: "Database",
    icon: SiPostgresql,
    experience: "1+ years",
    description: "Complex queries, relationships, indexing, and database optimization",
    projects: 6,
    color: "#336791"
  },
  {
    name: "Prisma",
    level: 80,
    category: "Database",
    icon: SiPrisma,
    experience: "1+ years",
    description: "Schema design, migrations, type-safe database access, and query optimization",
    projects: 6,
    color: "#2D3748"
  },
  {
    name: "Supabase",
    level: 75,
    category: "Database",
    icon: SiSupabase,
    experience: "1+ years",
    description: "Authentication, real-time subscriptions, and database management",
    projects: 4,
    color: "#3ECF8E"
  },

  // Development Tools
  {
    name: "Git",
    level: 88,
    category: "Tools",
    icon: SiGit,
    experience: "2+ years",
    description: "Version control, branching strategies, merge conflicts, and collaboration",
    projects: 15,
    color: "#F05032"
  },
  {
    name: "GitHub",
    level: 85,
    category: "Tools",
    icon: SiGithub,
    experience: "2+ years",
    description: "Repository management, pull requests, actions, and project collaboration",
    projects: 15,
    color: "#181717"
  },
  {
    name: "VS Code",
    level: 92,
    category: "Tools",
    icon: Code, // Using lucide-react Code icon as VS Code brand icon is not available
    experience: "2+ years",
    description: "Extensions, debugging, integrated terminal, and productivity optimization",
    projects: 15,
    color: "#007ACC"
  },
  {
    name: "Vite",
    level: 80,
    category: "Tools",
    icon: SiVite,
    experience: "1+ years",
    description: "Fast build tool, hot module replacement, and modern development workflow",
    projects: 8,
    color: "#646CFF"
  },
  {
    name: "Vercel",
    level: 85,
    category: "Tools",
    icon: SiVercel,
    experience: "1+ years",
    description: "Deployment, serverless functions, and performance optimization",
    projects: 10,
    color: "#000000"
  },

  // Testing
  {
    name: "Jest",
    level: 75,
    category: "Testing",
    icon: SiVitest,
    experience: "1+ years",
    description: "Unit testing, mocking, and test-driven development practices",
    projects: 6,
    color: "#C21325"
  },
  {
    name: "React Testing Library",
    level: 70,
    category: "Testing",
    icon: SiReact,
    experience: "1+ years",
    description: "Component testing, user interaction testing, and accessibility testing",
    projects: 4,
    color: "#61DAFB"
  },
  {
    name: "Vitest",
    level: 72,
    category: "Testing",
    icon: SiVitest,
    experience: "6 months",
    description: "Fast unit testing, snapshot testing, and modern testing workflows",
    projects: 3,
    color: "#6E9F18"
  },

  // Design Tools
  {
    name: "Figma",
    level: 78,
    category: "Design",
    icon: SiFigma,
    experience: "1+ years",
    description: "UI/UX design, prototyping, design systems, and developer handoff",
    projects: 8,
    color: "#F24E1E"
  }
];

// Professional Skills (Soft Skills)
export const professionalSkills = [
  {
    name: "Problem Solving",
    level: 90,
    icon: Brain,
    description: "Breaking down complex problems into manageable solutions",
    examples: ["Debugging complex application issues", "Optimizing database queries", "Architecting scalable solutions"]
  },
  {
    name: "Communication",
    level: 85,
    icon: MessageSquare,
    description: "Clear technical communication with team members and stakeholders",
    examples: ["Technical documentation", "Code reviews", "Client presentations"]
  },
  {
    name: "Team Collaboration",
    level: 88,
    icon: Users2,
    description: "Working effectively in cross-functional development teams",
    examples: ["Agile development", "Pair programming", "Knowledge sharing"]
  },
  {
    name: "Time Management",
    level: 82,
    icon: Timer,
    description: "Efficiently managing multiple projects and meeting deadlines",
    examples: ["Sprint planning", "Task prioritization", "Deadline management"]
  },
  {
    name: "Attention to Detail",
    level: 90,
    icon: Eye,
    description: "Ensuring code quality and pixel-perfect implementations",
    examples: ["Code reviews", "UI/UX implementation", "Testing and debugging"]
  },
  {
    name: "Adaptability",
    level: 88,
    icon: Repeat,
    description: "Quickly learning new technologies and adapting to changing requirements",
    examples: ["Learning new frameworks", "Technology migrations", "Requirement changes"]
  }
];

// Currently Learning
export const currentlyLearning = [
  {
    name: "Docker",
    progress: 60,
    icon: SiDocker,
    description: "Containerization and deployment strategies",
    estimatedCompletion: "2 months",
    resources: ["Docker Documentation", "Container Orchestration Course"],
    color: "#2496ED"
  },
  {
    name: "GraphQL",
    progress: 45,
    icon: Code,
    description: "Query language for APIs and runtime for executing queries",
    estimatedCompletion: "3 months",
    resources: ["GraphQL Documentation", "Apollo Client Tutorial"],
    color: "#E10098"
  },
  {
    name: "AWS Services",
    progress: 30,
    icon: Settings,
    description: "Cloud computing services and serverless architecture",
    estimatedCompletion: "4 months",
    resources: ["AWS Documentation", "Cloud Practitioner Course"],
    color: "#FF9900"
  },
  {
    name: "React Native",
    progress: 25,
    icon: SiReact,
    description: "Mobile app development using React principles",
    estimatedCompletion: "5 months",
    resources: ["React Native Documentation", "Mobile Development Course"],
    color: "#61DAFB"
  }
];

// Skill Categories for filtering
export const skillCategories = [
  { name: "All", icon: Globe, count: technicalSkills.length },
  { name: "Frontend", icon: Monitor, count: technicalSkills.filter(s => s.category === "Frontend" || s.category === "Styling" || s.category === "Animation").length },
  { name: "Backend", icon: Database, count: technicalSkills.filter(s => s.category === "Backend").length },
  { name: "Database & DevOps", icon: Settings, count: technicalSkills.filter(s => s.category === "Database" || s.category === "Tools").length },
  { name: "Testing", icon: CheckCircle, count: technicalSkills.filter(s => s.category === "Testing").length }
];

// Skills Statistics
export const skillsStats = {
  totalTechnicalSkills: technicalSkills.length,
  totalProfessionalSkills: professionalSkills.length,
  currentlyLearningCount: currentlyLearning.length,
  averageSkillLevel: Math.round(technicalSkills.reduce((acc, skill) => acc + skill.level, 0) / technicalSkills.length),
  expertSkills: technicalSkills.filter(skill => skill.level >= 90).length,
  advancedSkills: technicalSkills.filter(skill => skill.level >= 80 && skill.level < 90).length,
  intermediateSkills: technicalSkills.filter(skill => skill.level >= 70 && skill.level < 80).length
};
