import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { scrollToTopInstant } from '@/components/ScrollToTop'
import { Button } from '@/components/ui/button'
import { BaseSectionProps } from './types'
import { technicalSkills } from '@/data/skills/skillsData'

export default function CoreTechnologiesSection({ className }: BaseSectionProps) {
  const navigate = useNavigate()

  const handleNavigation = (path: string) => {
    scrollToTopInstant()
    navigate(path)
  }

  // Create multiple sets of technologies for seamless looping
  const allTechnologies = [...technicalSkills, ...technicalSkills, ...technicalSkills]

  // --- CONFIGURATION FOR SCROLLER ---
  const ICON_WIDTH = 112 // Corresponds to w-28
  const GAP_X = 32 // Corresponds to gap-x-8
  const ITEM_TOTAL_WIDTH = ICON_WIDTH + GAP_X
  const ANIMATION_DURATION = technicalSkills.length * 2.2 // Slightly slowed for a more premium feel

  return (
    <section className={`py-20 lg:py-32 ${className || ''}`}>
      {/* Header Section */}
      <div className="container mx-auto px-4 text-center mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-2"
        >
          <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
            MY TECH STACK
          </span>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6 [filter:drop-shadow(0_0_20px_rgba(255,255,255,0.2))]"
        >
          Core{' '}
          <span className="relative font-apparel font-black italic -skew-x-12 inline-block pb-3 bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift">
            technologies
          </span>
        </motion.h2>
      </div>

      {/* --- UPDATED: Enhanced Infinite Scroller with Spotlight --- */}
      <div
        className="relative overflow-hidden py-12 mb-16"
        style={{
          maskImage: 'radial-gradient(circle at center, white 0%, white 50%, transparent 100%)',
        }}
      >
        <motion.div
          className="flex items-center gap-x-8"
          animate={{
            x: [0, -ITEM_TOTAL_WIDTH * technicalSkills.length]
          }}
          transition={{
            x: {
              repeat: Infinity,
              repeatType: "loop",
              duration: ANIMATION_DURATION,
              ease: "linear",
            },
          }}
        >
          {allTechnologies.map((skill, index) => {
            const IconComponent = skill.icon
            const isDarkIcon = skill.color === '#000000' || skill.color === 'black';
            const glowColor = isDarkIcon ? '#FFFFFF' : skill.color;

            return (
              <motion.div
                key={`${skill.name}-${index}`}
                className="flex flex-shrink-0 flex-col items-center space-y-2 w-28 group"
                whileHover={{ scale: 1.15 }}
                transition={{ duration: 0.2, ease: "easeOut" }}
              >
                <div className="w-16 h-16 flex items-center justify-center rounded-2xl bg-gradient-to-br from-background/80 to-background/40 transition-all duration-300 backdrop-blur-sm">
                  <IconComponent
                    className="w-9 h-9 transition-all duration-300 group-hover:[filter:drop-shadow(0_0_12px_var(--glow-color))] "
                    style={{
                      '--glow-color': glowColor,
                      color: skill.color,
                      filter: `drop-shadow(0 0 8px ${glowColor}55)`
                    } as React.CSSProperties}
                  />
                </div>
                <p className="text-sm font-semibold text-muted-foreground group-hover:text-foreground transition-colors duration-300 text-center">
                  {skill.name}
                </p>
              </motion.div>
            )
          })}
        </motion.div>
      </div>

      {/* --- UPDATED: CTA Button with Enhanced Hover --- */}
      <div className="container mx-auto px-4 text-center">
        <Button
          onClick={() => handleNavigation('/skills')}
          className="group relative bg-neutral-900/50 hover:bg-neutral-800/50 text-white shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] hover:shadow-[inset_0_0_25px_0_rgba(255,255,255,0.12)] transition-all duration-300 ease-in-out px-10 py-4 text-lg font-medium border border-neutral-700/50 hover:border-purple-500/50 rounded-xl backdrop-blur-lg hover:shadow-purple-500/20"
        >
          <span className="relative z-10">Explore my full skillset</span>
        </Button>
      </div>
    </section>
  )
}
