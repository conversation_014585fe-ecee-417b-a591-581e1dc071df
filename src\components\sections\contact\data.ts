import {
  Mail,
  Phone,
  MapPin,
  Clock,
  Gith<PERSON>,
  Linkedin,
  Twitter,
  Instagram,
  Award,
  Users,
  Star,
  Coffee
} from "lucide-react"
import { ContactMethod, SocialLink, ContactStat } from "./types"
import { userData } from '@/data/personal/userData'

export const contactMethods: ContactMethod[] = [
  {
    icon: Mail,
    label: "Email",
    value: userData.email,
    href: `mailto:${userData.email}`,
    description: "Send me an email anytime"
  },
  {
    icon: Phone,
    label: "Phone",
    value: userData.phone,
    href: `tel:${userData.phone.replace(/\D/g, '')}`,
    description: "Call me during business hours"
  },
  {
    icon: MapPin,
    label: "Location",
    value: userData.location,
    href: `https://maps.google.com/?q=${encodeURIComponent(userData.location)}`,
    description: "Available for local meetings"
  },
  {
    icon: Clock,
    label: "Response Time",
    value: userData.workingHours.responseTime.email,
    href: "#",
    description: "I'll get back to you quickly"
  }
]

export const socialLinks: SocialLink[] = [
  {
    icon: Github,
    label: "GitHub",
    href: userData.social.github,
    color: "hover:text-gray-900 dark:hover:text-white"
  },
  {
    icon: Linkedin,
    label: "LinkedIn",
    href: userData.social.linkedin,
    color: "hover:text-blue-600"
  },
  {
    icon: Twitter,
    label: "Twitter",
    href: userData.social.twitter,
    color: "hover:text-blue-400"
  },
  {
    icon: Instagram,
    label: "Instagram",
    href: userData.social.instagram,
    color: "hover:text-pink-600"
  }
]

export const contactStats: ContactStat[] = [
  { label: "Projects Completed", value: userData.stats.projectsCompleted, icon: Award },
  { label: "Happy Clients", value: userData.stats.happyClients, icon: Users },
  { label: "5-Star Reviews", value: userData.stats.fiveStarReviews, icon: Star },
  { label: "Years Experience", value: userData.stats.yearsExperience, icon: Coffee }
]

export const inquiryTypes = userData.inquiryTypes

export const availabilityStatus = {
  available: userData.availability.status === "available",
  message: userData.availability.message,
  nextAvailable: userData.availability.nextAvailable
}

export const responseTime = userData.workingHours.responseTime

export const workingHours = {
  timezone: userData.workingHours.timezone,
  days: userData.workingHours.days,
  hours: userData.workingHours.hours
}
