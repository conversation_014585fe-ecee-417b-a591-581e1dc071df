import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"

// Types
interface ContributionDay {
  date: string
  count: number
  level: number
}

interface GitHubContributionDay {
  date: string
  contributionCount: number
}

interface ContributionWeek {
  contributionDays: GitHubContributionDay[]
}

interface GitHubContributionsResponse {
  data: {
    user: {
      contributionsCollection: {
        contributionCalendar: {
          totalContributions: number
          weeks: ContributionWeek[]
        }
      }
    }
  }
  errors?: Array<{ message: string }>
}

// GitHub GraphQL query for contributions
const CONTRIBUTIONS_QUERY = `
  query($username: String!, $from: DateTime!, $to: DateTime!) {
    user(login: $username) {
      contributionsCollection(from: $from, to: $to) {
        contributionCalendar {
          totalContributions
          weeks {
            contributionDays {
              date
              contributionCount
            }
          }
        }
      }
    }
  }
`

export default function GitHubContributions() {
  const [contributions, setContributions] = useState<ContributionDay[]>([])
  const [totalContributions, setTotalContributions] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [hoveredDay, setHoveredDay] = useState<ContributionDay | null>(null)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())

  const username = import.meta.env.VITE_GITHUB_USERNAME || "cjjutba"
  const token = import.meta.env.VITE_GITHUB_TOKEN

  useEffect(() => {
    fetchContributions()
  }, [selectedYear])

  const fetchContributions = async () => {
    if (!token) {
      setError("GitHub token not found. Please add VITE_GITHUB_TOKEN to your .env file.")
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      // Calculate date range for the selected year
      const from = new Date(selectedYear, 0, 1).toISOString()
      const to = new Date(selectedYear, 11, 31, 23, 59, 59).toISOString()

      const response = await fetch('https://api.github.com/graphql', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: CONTRIBUTIONS_QUERY,
          variables: { username, from, to }
        })
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`GitHub API error: ${response.status} - ${errorText}`)
      }

      const data: GitHubContributionsResponse = await response.json()

      // Check for GraphQL errors
      if (data.errors) {
        throw new Error(`GraphQL error: ${data.errors[0]?.message || 'Unknown error'}`)
      }
      
      if (data.data?.user?.contributionsCollection) {
        const { contributionCalendar } = data.data.user.contributionsCollection

        // Flatten weeks into days and calculate levels
        const allDays: ContributionDay[] = contributionCalendar.weeks.flatMap(week =>
          week.contributionDays.map(day => {
            const count = day.contributionCount
            // Calculate level based on GitHub's typical thresholds
            let level = 0
            if (count >= 1) level = 1
            if (count >= 3) level = 2
            if (count >= 6) level = 3
            if (count >= 10) level = 4

            return {
              date: day.date,
              count: count,
              level: level
            }
          })
        )

        setContributions(allDays)
        setTotalContributions(contributionCalendar.totalContributions)
      } else {
        throw new Error("Invalid response from GitHub API")
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch contributions')
    } finally {
      setLoading(false)
    }
  }

  const calculateCurrentStreak = (): number => {
    let streak = 0
    for (let i = contributions.length - 1; i >= 0; i--) {
      if (contributions[i].count > 0) {
        streak++
      } else {
        break
      }
    }
    return streak
  }

  const calculateLongestStreak = (): number => {
    let maxStreak = 0
    let currentStreak = 0
    
    contributions.forEach(day => {
      if (day.count > 0) {
        currentStreak++
        maxStreak = Math.max(maxStreak, currentStreak)
      } else {
        currentStreak = 0
      }
    })
    
    return maxStreak
  }

  const getMonthLabels = () => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    const weeks = organizeContributionsIntoWeeks()
    const monthLabels: string[] = []
    let lastMonth = -1

    // Create month labels that align with the weeks
    weeks.forEach((week, index) => {
      const firstDayOfWeek = week.find(day => day.date)
      if (firstDayOfWeek && firstDayOfWeek.date) {
        const date = new Date(firstDayOfWeek.date)
        const currentMonth = date.getMonth()

        // Show month label if it's a new month or the first week
        if (currentMonth !== lastMonth || index === 0) {
          monthLabels.push(months[currentMonth])
          lastMonth = currentMonth
        } else {
          monthLabels.push('')
        }
      } else {
        monthLabels.push('')
      }
    })

    return monthLabels
  }



  // Organize contributions into weeks for proper GitHub-style display
  const organizeContributionsIntoWeeks = () => {
    const weeks: ContributionDay[][] = []
    let currentWeek: ContributionDay[] = []

    contributions.forEach((day, index) => {
      const dayOfWeek = new Date(day.date).getDay() // 0 = Sunday, 6 = Saturday

      // If this is the first day or we're starting a new week (Sunday)
      if (index === 0) {
        // Fill empty days at the beginning of the first week
        for (let i = 0; i < dayOfWeek; i++) {
          currentWeek.push({ date: '', count: 0, level: 0 })
        }
      }

      currentWeek.push(day)

      // If it's Saturday or the last day, complete the week
      if (dayOfWeek === 6 || index === contributions.length - 1) {
        // Fill empty days at the end of the last week
        while (currentWeek.length < 7) {
          currentWeek.push({ date: '', count: 0, level: 0 })
        }
        weeks.push([...currentWeek])
        currentWeek = []
      }
    })

    return weeks
  }

  if (loading) {
    return (
      <Card className="border-none shadow-none bg-transparent">
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-muted-foreground">Loading GitHub contributions...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="border-none shadow-none bg-transparent">
        <CardContent className="p-6">
          <div className="text-center text-red-500">
            <p className="font-medium">Error loading GitHub contributions</p>
            <p className="text-sm mt-2">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const currentStreak = calculateCurrentStreak()
  const longestStreak = calculateLongestStreak()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <Card className="border-none shadow-none bg-transparent overflow-hidden">
        <CardContent className="p-6 space-y-12">
          {/* Section Header */}
          <div className="text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-2"
            >
              <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
                A GLIMPSE INTO MY ACTIVITY
              </span>
            </motion.div>
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight"
            >
              My{' '}
              <span className="relative font-apparel font-black italic -skew-x-12 inline-block bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift [filter:drop-shadow(0_0_15px_rgba(148,56,213,0.5))_drop-shadow(0_0_1.5px_rgba(0,0,0,0.2))]">
                Contributions
              </span>
            </motion.h2>
          </div>

          {/* GitHub-style Contribution Graph */}
          <div className="relative rounded-2xl bg-neutral-900/50 p-6 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] overflow-x-auto">
            {/* Header with stats and year selector */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium text-foreground">
                  {totalContributions} contributions in {selectedYear}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground">Year:</span>
                <select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                  className="bg-background border border-border rounded px-2 py-1 text-sm"
                  title="Select year to view contributions"
                >
                  {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i).map(year => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Month labels - positioned above the grid with better spacing */}
            <div className="flex mb-4">
              <div className="w-10"></div> {/* Space for day labels */}
              <div className="flex gap-1 min-w-0">
                {getMonthLabels().map((month, index) => (
                  <div key={`${month}-${index}`} className="w-3.5 text-xs font-medium text-muted-foreground/80 text-left">
                    {month}
                  </div>
                ))}
              </div>
            </div>

            {/* Main contribution grid */}
            <div className="flex">
              {/* Day labels with improved styling */}
              <div className="flex flex-col justify-between text-xs font-medium text-muted-foreground/70 pr-3 py-1 w-10 h-28">
                <div className="h-3.5 flex items-center"></div> {/* Sun - hidden */}
                <div className="h-3.5 flex items-center">Mon</div>
                <div className="h-3.5 flex items-center"></div> {/* Tue - hidden */}
                <div className="h-3.5 flex items-center">Wed</div>
                <div className="h-3.5 flex items-center"></div> {/* Thu - hidden */}
                <div className="h-3.5 flex items-center">Fri</div>
                <div className="h-3.5 flex items-center"></div> {/* Sat - hidden */}
              </div>

              {/* Contribution squares with enhanced styling */}
              <div className="flex-1 min-w-0">
                <div className="flex gap-1">
                  {organizeContributionsIntoWeeks().map((week, weekIndex) => (
                    <div key={weekIndex} className="flex flex-col gap-1">
                      {week.map((day, dayIndex) => (
                        <div
                          key={day.date || `empty-${weekIndex}-${dayIndex}`}
                          className={cn(
                            "w-3.5 h-3.5 rounded-[3px] transition-all duration-300 ease-out",
                            !day.date && "opacity-0", // Empty days are invisible
                            day.date && "cursor-pointer hover:scale-110 hover:z-10 relative",
                            day.level === 0 && day.date && "bg-muted/60 hover:bg-muted/80 border border-border/30",
                            day.level === 1 && "bg-primary/15 hover:bg-primary/25 border border-primary/20 shadow-sm",
                            day.level === 2 && "bg-primary/35 hover:bg-primary/45 border border-primary/30 shadow-sm",
                            day.level === 3 && "bg-primary/55 hover:bg-primary/65 border border-primary/40 shadow-md",
                            day.level === 4 && "bg-primary/75 hover:bg-primary/85 border border-primary/50 shadow-md",
                            hoveredDay?.date === day.date && day.date && "ring-2 ring-primary/50 ring-offset-1 ring-offset-background scale-110 z-10"
                          )}
                          onMouseEnter={() => day.date && setHoveredDay(day)}
                          onMouseLeave={() => setHoveredDay(null)}
                          title={day.date ? `${day.count} contributions on ${new Date(day.date).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}` : ''}
                        />
                      ))}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Enhanced Legend */}
            <div className="flex items-center justify-between mt-6 pt-4 border-t border-border/30">
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground/70 hover:text-muted-foreground transition-colors cursor-pointer">
                  Learn how we count contributions
                </span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-xs font-medium text-muted-foreground/70">Less</span>
                <div className="flex items-center gap-1.5">
                  <div className="w-3.5 h-3.5 bg-muted/60 rounded-[3px] border border-border/30"></div>
                  <div className="w-3.5 h-3.5 bg-primary/15 rounded-[3px] border border-primary/20"></div>
                  <div className="w-3.5 h-3.5 bg-primary/35 rounded-[3px] border border-primary/30"></div>
                  <div className="w-3.5 h-3.5 bg-primary/55 rounded-[3px] border border-primary/40"></div>
                  <div className="w-3.5 h-3.5 bg-primary/75 rounded-[3px] border border-primary/50"></div>
                </div>
                <span className="text-xs font-medium text-muted-foreground/70">More</span>
              </div>
            </div>

            {/* Tooltip */}
            <AnimatePresence>
              {hoveredDay && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full mb-2 bg-card border border-border rounded-lg px-3 py-2 shadow-lg z-10"
                >
                  <div className="text-sm font-medium text-foreground">
                    {hoveredDay.count} contributions on {new Date(hoveredDay.date).toLocaleDateString('en-US', { 
                      weekday: 'short', 
                      month: 'short', 
                      day: 'numeric', 
                      year: 'numeric' 
                    })}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Additional Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4">
              <div className="text-lg font-bold text-foreground">{currentStreak}</div>
              <div className="text-sm text-muted-foreground">Current streak</div>
            </div>
            <div className="text-center p-4">
              <div className="text-lg font-bold text-foreground">{longestStreak}</div>
              <div className="text-sm text-muted-foreground">Longest streak</div>
            </div>
            <div className="text-center p-4">
              <div className="text-lg font-bold text-foreground">{Math.round(totalContributions / 12)}</div>
              <div className="text-sm text-muted-foreground">Avg per month</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}