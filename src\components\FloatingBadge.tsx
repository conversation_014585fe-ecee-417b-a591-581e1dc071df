import React from 'react';
import { motion } from 'framer-motion';

// A clean 4-point star icon, resized to fit the larger badge.
const StarIcon = () => (
  <svg
    width="28" // ✨ Increased size for better proportion
    height="28" // ✨ Increased size for better proportion
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="text-white"
  >
    <motion.path
      d="M12 2L14.5 9.5L22 12L14.5 14.5L12 22L9.5 14.5L2 12L9.5 9.5L12 2Z"
      fill="currentColor"
      initial={{ scale: 0.9, rotate: 0 }}
      animate={{ scale: [0.9, 1, 0.9], rotate: [0, 90, 180] }}
      transition={{
        duration: 4,
        ease: "easeInOut",
        repeat: Infinity,
      }}
    />
  </svg>
);

interface FloatingBadgeProps {
  dragConstraintsRef: React.RefObject<HTMLElement>;
  className?: string; // Allow passing custom classes for positioning
}

export default function FloatingBadge({ dragConstraintsRef, className }: FloatingBadgeProps) {
  return (
    <motion.div
      drag
      dragConstraints={dragConstraintsRef}
      dragMomentum={false}
      animate={{ y: [0, -5, 0, 5, 0] }} // Slightly reduced float distance
      transition={{ duration: 8, ease: "easeInOut", repeat: Infinity }}
      // ✨ Increased padding and container size to prevent the thicker border from being clipped
      className={`relative flex-shrink-0 w-32 h-32 p-4 cursor-grab active:cursor-grabbing z-20 ${className || ''}`}
    >
      <div className="relative w-full h-full">
        <motion.svg
          className="w-full h-full"
          viewBox="0 0 150 150"
          initial={{ rotate: 0 }}
          animate={{ rotate: 360 }}
          transition={{ duration: 25, ease: "linear", repeat: Infinity }}
        >
          <defs>
            <linearGradient id="badgeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="hsl(var(--primary))" />
              <stop offset="100%" stopColor="hsl(var(--accent))" />
            </linearGradient>
            {/* ✨ Adjusted path radius to push text further inside for more breathing room */}
            <path
              id="circlePath"
              d="M 75, 75 m -50, 0 a 50,50 0 1,1 100,0 a 50,50 0 1,1 -100,0"
            />
          </defs>
          {/* ✨ Thicker border stroke with adjusted radius to prevent clipping */}
          <circle cx="75" cy="75" r="70" stroke="url(#badgeGradient)" strokeWidth="10" fill="none" />
          {/* ✨ Adjusted background circle radius to fit inside the new border */}
          <circle cx="75" cy="75" r="65" fill="hsl(var(--background))" />
          {/* ✨ Adjusted font size, spacing, and text to fit the new path and content */}
          <text fill="hsl(var(--foreground))" style={{ fontSize: '12px', letterSpacing: '4px' }}>
            <textPath
              xlinkHref="#circlePath"
              startOffset="50%"
              textAnchor="middle"
              textLength="310" 
            >
              • OPEN TO WORK • AVAILABLE FOR HIRE
            </textPath>
          </text>
        </motion.svg>
        
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
          <StarIcon />
        </div>
      </div>
    </motion.div>
  );
}
