import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { BaseSectionProps, staggerContainerVariants, itemVariants } from './types'
import { technicalSkills, skillCategories } from '@/data/skills/skillsData'
import { renderIcon, getSkillsByCategory, getSkillLevelColor } from './utils'

export default function TechnicalSkillsSection({ className }: BaseSectionProps) {
  const [activeCategory, setActiveCategory] = useState("All")
  const [selectedSkill, setSelectedSkill] = useState<string | null>(null)

  const filteredSkills = getSkillsByCategory(technicalSkills, activeCategory)

  return (
    <section className={cn('py-24 sm:py-32', className)}>
      {/* Section Header */}
      <div className="mb-20 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-2"
        >
          <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
            A VIEW OF MY CAPABILITIES
          </span>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight"
        >
          Technical{' '}
          <span className="relative font-apparel font-black italic -skew-x-12 inline-block bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift [filter:drop-shadow(0_0_15px_rgba(148,56,213,0.5))_drop-shadow(0_0_1.5px_rgba(0,0,0,0.2))]">
            Expertise
          </span>
        </motion.h2>
      </div>

      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={staggerContainerVariants}
        className="max-w-6xl mx-auto"
      >
        {/* Category Filter */}
        <motion.div variants={itemVariants} className="mb-12">
          <div className="flex flex-wrap gap-3 justify-center">
            {skillCategories.map((category) => (
              <button
                key={category.name}
                onClick={() => setActiveCategory(category.name)}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 rounded-full border transition-all duration-300 hover:scale-105",
                  activeCategory === category.name
                    ? "bg-primary text-primary-foreground border-primary shadow-lg"
                    : "bg-card hover:bg-primary/10 text-muted-foreground border-border hover:border-primary/50"
                )}
              >
                {renderIcon(category.icon, "w-4 h-4")}
                <span className="font-medium">{category.name}</span>
                <Badge
                  variant="secondary"
                  className={cn(
                    "ml-1 text-xs",
                    activeCategory === category.name
                      ? "bg-primary-foreground/20 text-primary-foreground"
                      : "bg-muted text-muted-foreground"
                  )}
                >
                  {category.count}
                </Badge>
              </button>
            ))}
          </div>
        </motion.div>

        {/* Skills Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeCategory}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {filteredSkills.map((skill, index) => (
              <motion.div
                key={skill.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <Card
                  className={cn(
                    'group h-full cursor-pointer transition-all duration-300 transform hover:scale-[1.02]',
                    'bg-neutral-900/50 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] rounded-2xl border-transparent',
                    selectedSkill === skill.name && "ring-2 ring-primary/50"
                  )}
                  onClick={() => setSelectedSkill(selectedSkill === skill.name ? null : skill.name)}
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-lg bg-neutral-800/60 flex items-center justify-center">
                          {renderIcon(skill.icon, "w-5 h-5")}
                        </div>
                        <div>
                          <CardTitle className="text-lg font-bold group-hover:text-primary transition-colors duration-300">
                            {skill.name}
                          </CardTitle>
                          <CardDescription className="text-sm">
                            {skill.experience}
                          </CardDescription>
                        </div>
                      </div>
                      <Badge
                        variant="secondary"
                        className={cn("font-bold", getSkillLevelColor(skill.level))}
                      >
                        {skill.level}%
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Progress value={skill.level} className="h-2" />
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {skill.description}
                    </p>

                    <AnimatePresence>
                      {selectedSkill === skill.name && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="pt-4"
                        >
                          <div className="space-y-3">
                            <Separator />
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-muted-foreground">Projects completed:</span>
                              <Badge variant="outline" className="font-medium">
                                {skill.projects}
                              </Badge>
                            </div>
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-muted-foreground">Category:</span>
                              <Badge variant="secondary" className="font-medium">
                                {skill.category}
                              </Badge>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>
      </motion.div>
    </section>
  )
}
