// Components
export { HeroSection } from "./HeroSection"
export { ContactFormSection } from "./ContactFormSection"
export { ContactInfoSection } from "./ContactInfoSection"
export { CallToActionSection } from "./CallToActionSection"

// Types
export type {
  BaseSectionProps,
  ContactMethod,
  SocialLink,
  ContactStat,
  FormData,
  FormErrors
} from "./types"

// Data
export {
  contactMethods,
  socialLinks,
  contactStats,
  inquiryTypes,
  availabilityStatus,
  responseTime,
  workingHours
} from "./data"

// Utils
export {
  createNavigationHandler,
  validateForm,
  handleFormSubmit,
  isValidEmail,
  formatPhoneNumber,
  getInquiryTypeColor
} from "./utils"

// Animation variants
export {
  containerVariants,
  itemVariants,
  staggerContainerVariants
} from "./types"
