import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { BaseSectionProps, containerVariants } from './types';
import { userData } from '@/data/personal/userData';
// The enhancedContent import is no longer needed for the title.
// import { enhancedContent } from '@/data/personal/enhancedContent';
import { Linkedin, Github, Facebook } from 'lucide-react';

const socialLinks = [
  {
    name: 'LinkedIn',
    href: 'https://www.linkedin.com/in/cjjutba',
    icon: Linkedin,
  },
  {
    name: 'GitHub',
    href: 'https://github.com/cjjutba',
    icon: Github,
  },
  {
    name: 'Facebook',
    href: 'https://www.facebook.com/profile.php?id=61558829783116',
    icon: Facebook,
  },
];

const carouselItems = [
  {
    id: 1,
    src: '/about_profile.jpg',
    caption: 'I Code',
  },
  {
    id: 2,
    src: '/about_profile.jpg',
    caption: 'I Lift',
  },
  {
    id: 3,
    src: '/about_profile.jpg',
    caption: 'I Travel',
  },
];

export default function HeroSection({ className }: BaseSectionProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const changeSlide = (direction: number) => {
    setCurrentImageIndex((prev) => {
      const newIndex = (prev + direction + carouselItems.length) % carouselItems.length;
      return newIndex;
    });
  };

  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: any) => {
    const swipeThreshold = 50;
    const swipePower = Math.abs(info.velocity.x) * 0.1;

    if (info.offset.x < -swipeThreshold - swipePower) {
      changeSlide(1); // Swipe left, go to next image
    } else if (info.offset.x > swipeThreshold + swipePower) {
      changeSlide(-1); // Swipe right, go to previous image
    }
  };

  return (
    <motion.section
      // UPDATED: Added padding and margin to match the dashboard hero section for consistency.
      className={`pt-12 md:pt-24 mb-40 md:mb-40 w-full ${className || ''}`}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 lg:gap-24 items-center">
        {/* Left Column - Content */}
        <div className="order-2 lg:order-1">
          <div className="space-y-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4 }}
            >
              <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
                MORE ABOUT ME
              </span>
            </motion.div>
            
            {/* --- UPDATED HEADLINE --- */}
            {/* The h1 element is updated with the new headline and consistent styling. */}
            <motion.h1
              className="text-display text-4xl md:text-5xl lg:text-[3.5rem] xl:text-6xl font-bold text-foreground leading-tight"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.05 }}
            >
              Building systems that{' '}
              <span className="relative font-apparel font-black italic -skew-x-12 inline-block bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift [filter:drop-shadow(0_0_15px_rgba(148,56,213,0.5))_drop-shadow(0_0_1.5px_rgba(0,0,0,0.2))]">
                users love
              </span>
            </motion.h1>
            
            <motion.p
              className="text-lg md:text-xl text-muted-foreground leading-relaxed max-w-2xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.1 }}
            >
              I'm {userData.name}, a Full-stack developer who cares deeply about user experience and creating systems that just work seamlessly.
            </motion.p>
            
            <motion.div
              className="flex items-center gap-5 pt-2"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.15 }}
            >
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={social.name}
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  <social.icon size={20} />
                </a>
              ))}
            </motion.div>

          </div>
        </div>

        {/* Right Column - Interactive Image Carousel */}
        <motion.div
          className="order-1 lg:order-2 flex flex-col items-center justify-center"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.4, delay: 0.15 }}
        >
          <div className="relative w-full h-96 md:h-[28rem] flex items-center justify-center" style={{ perspective: '1000px' }}>
            <AnimatePresence>
              {carouselItems.map((item, index) => {
                const offset = index - currentImageIndex;
                const isCurrent = offset === 0;
                const isPrevious = offset === -1 || (currentImageIndex === 0 && index === carouselItems.length - 1);
                const isNext = offset === 1 || (currentImageIndex === carouselItems.length - 1 && index === 0);

                let style = {
                  zIndex: 0,
                  x: '100%',
                  scale: 0.7,
                  opacity: 0,
                  rotateY: 0,
                  filter: 'saturate(0) brightness(0)',
                };
                if (isCurrent) {
                  style = { zIndex: 3, x: '0%', scale: 1, opacity: 1, rotateY: 0, filter: 'saturate(1) brightness(1)' };
                } else if (isPrevious) {
                  style = { zIndex: 2, x: '-40%', scale: 0.8, opacity: 0.8, rotateY: 15, filter: 'saturate(0.7) brightness(0.7)' };
                } else if (isNext) {
                  style = { zIndex: 1, x: '40%', scale: 0.8, opacity: 0.8, rotateY: -15, filter: 'saturate(0.7) brightness(0.7)' };
                }
                
                return (
                  <motion.div
                    key={item.id}
                    className="absolute w-56 h-80 md:w-64 md:h-96 cursor-grab active:cursor-grabbing"
                    style={{ transformStyle: "preserve-3d" }}
                    initial={false}
                    animate={style}
                    transition={{ type: 'spring', stiffness: 200, damping: 25 }}
                    drag="x"
                    dragConstraints={{ left: 0, right: 0 }}
                    onDragEnd={handleDragEnd}
                  >
                    <img
                      src={item.src}
                      alt={item.caption}
                      className="w-full h-full object-cover rounded-2xl shadow-lg border border-border/10 select-none"
                      draggable="false"
                    />
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </div>
          
          <div className="mt-2 text-center h-6">
            <AnimatePresence mode="wait">
              <motion.p
                key={currentImageIndex}
                className="text-md text-muted-foreground font-semibold"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                {carouselItems[currentImageIndex].caption}
              </motion.p>
            </AnimatePresence>
          </div>
        </motion.div>
      </div>
    </motion.section>
  );
}
