import React from "react"
import { motion } from "framer-motion"
import { ExternalLink, CheckCircle } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { BaseSectionProps, itemVariants, staggerContainerVariants } from "./types"
import { contactMethods, socialLinks, availabilityStatus, workingHours } from "./data"

export const ContactInfoSection: React.FC<BaseSectionProps> = ({ className }) => {
  return (
    <motion.div
      className={`space-y-6 ${className || ""}`}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      variants={staggerContainerVariants}
    >
      {/* Contact Methods */}
      <motion.div variants={itemVariants}>
        <Card className="border-border/50 bg-card/90 backdrop-blur-sm hover:shadow-soft-xl transition-all duration-300">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-xl">
              <div className="w-8 h-8 bg-gradient-to-br from-primary/20 to-primary/10 dark:from-primary/30 dark:to-primary/20 rounded-lg flex items-center justify-center">
                <ExternalLink className="w-4 h-4 text-primary" />
              </div>
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {contactMethods.map((method, index) => (
              <motion.div
                key={method.label}
                className="flex items-center gap-4 p-3 rounded-lg hover:bg-muted/50 transition-colors duration-200 group cursor-pointer"
                onClick={() => method.href !== "#" && window.open(method.href, '_blank')}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <div className="w-10 h-10 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/20 dark:group-hover:bg-primary/30 transition-colors duration-200">
                  <method.icon className="w-5 h-5 text-primary" />
                </div>
                <div className="flex-1">
                  <div className="font-medium text-foreground group-hover:text-primary transition-colors duration-200">
                    {method.value}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {method.description}
                  </div>
                </div>
                {method.href !== "#" && (
                  <ExternalLink className="w-4 h-4 text-muted-foreground group-hover:text-primary transition-colors duration-200" />
                )}
              </motion.div>
            ))}
          </CardContent>
        </Card>
      </motion.div>

      {/* Social Links */}
      <motion.div variants={itemVariants}>
        <Card className="border-border/50 bg-card/90 backdrop-blur-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl">Connect With Me</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              {socialLinks.map((social, index) => (
                <motion.button
                  key={social.label}
                  onClick={() => window.open(social.href, '_blank')}
                  className="flex items-center gap-3 p-3 rounded-lg border border-border/50 hover:border-primary/30 bg-background/50 hover:bg-background/80 transition-all duration-200 group"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <social.icon className={`w-5 h-5 text-muted-foreground group-hover:text-primary transition-colors duration-200 ${social.color}`} />
                  <span className="text-sm font-medium text-foreground group-hover:text-primary transition-colors duration-200">
                    {social.label}
                  </span>
                </motion.button>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  )
}
