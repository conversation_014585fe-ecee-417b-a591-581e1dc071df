import { LucideIcon } from "lucide-react"
import { IconType } from "react-icons"

export interface BaseSectionProps {
  className?: string
}

export interface Project {
  id: number
  title: string
  description: string
  longDescription: string
  image: string
  technologies: string[]
  category: string
  type: string
  status: string
  featured: boolean
  liveUrl: string
  githubUrl: string
  highlight: string
  features: string[]
  challenges: string
  learnings: string
  duration: string
  teamSize: string
  year: string
  deviceType: "desktop" | "mobile" | "tablet"
}

export interface FilterCategory {
  name: string
  icon: LucideIcon
  count: number
}

export interface TechIcon {
  [key: string]: IconType | LucideIcon
}

// Animation variants
export const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05,
      delayChildren: 0.1
    }
  }
}

export const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4
    }
  }
}

export const staggerContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
      delayChildren: 0.1
    }
  }
}
