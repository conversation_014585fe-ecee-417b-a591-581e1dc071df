export interface Project {
  id: number
  title: string
  description: string
  longDescription: string
  image: string
  technologies: string[]
  category: "Full-Stack" | "Frontend" | "Backend" | "Mobile"
  type: "Web Application" | "Mobile App" | "API" | "Portfolio" | "Tool"
  status: "Completed" | "In Progress" | "Planning"
  featured: boolean
  liveUrl?: string
  githubUrl?: string
  highlight: string
  features: string[]
  challenges: string
  learnings: string
  duration: string
  teamSize: string
  year: string
  deviceType: "desktop" | "mobile" | "tablet"
}

export const projects: Project[] = [
  {
    id: 1,
    title: "E-commerce Platform",
    description: "A complete retail solution built from the ground up, demonstrating end-to-end development from PostgreSQL database design to React frontend implementation.",
    longDescription: "To handle complex product and order management, I engineered a robust RESTful API using Node.js and Express.js, with Prisma managing all PostgreSQL database interactions. This powerful backend provides real-time data to a fast, responsive React frontend built with TypeScript for type safety. The result is a seamless shopping experience where users can browse products, manage their cart, and complete purchases with confidence. Advanced features include real-time inventory management, personalized recommendations, and a responsive design that works flawlessly across all devices.",
    image: "/api/placeholder/600/400",
    technologies: ["React", "Next.js", "TypeScript", "Tailwind CSS", "Node.js", "PostgreSQL", "Prisma", "Stripe"],
    category: "Full-Stack",
    type: "Web Application",
    status: "Completed",
    featured: true,
    liveUrl: "https://shophub-demo.vercel.app",
    githubUrl: "https://github.com/cjjutba/ecommerce-platform",
    highlight: "Full-stack development with modern architecture",
    features: [
      "User authentication & authorization",
      "Shopping cart & checkout flow",
      "Payment processing with Stripe",
      "Admin dashboard for inventory management",
      "Responsive design for all devices",
      "Real-time order tracking"
    ],
    challenges: "Implementing secure payment processing and managing complex state across multiple user flows.",
    learnings: "Gained deep understanding of e-commerce architecture, payment systems, and user experience optimization.",
    duration: "3 months",
    teamSize: "Solo project",
    year: "2024",
    deviceType: "desktop"
  },
  {
    id: 2,
    title: "Task Management App",
    description: "A comprehensive productivity platform built with full-stack architecture, featuring real-time collaboration through WebSocket connections and complex database relationships.",
    longDescription: "I built a comprehensive task management system with a PostgreSQL database designed for complex relationships between users, projects, and tasks. The Express.js API handles real-time updates through WebSocket connections, while the React frontend provides an intuitive drag-and-drop interface. Key features include role-based permissions, real-time collaboration, file attachments, and detailed analytics. The application scales seamlessly from individual use to enterprise teams, with robust data synchronization ensuring consistency across all connected clients.",
    image: "/api/placeholder/600/400",
    technologies: ["Next.js", "React", "TypeScript", "Prisma", "PostgreSQL", "Tailwind CSS", "Socket.io"],
    category: "Full-Stack",
    type: "Web Application",
    status: "In Progress",
    featured: true,
    liveUrl: "https://taskflow-demo.vercel.app",
    githubUrl: "https://github.com/cjjutba/task-management",
    highlight: "Real-time collaboration with advanced project management",
    features: [
      "Real-time collaboration with Socket.io",
      "Drag-and-drop task management",
      "Team member management",
      "Project timeline visualization",
      "File sharing and comments",
      "Advanced filtering and search"
    ],
    challenges: "Implementing real-time synchronization across multiple users while maintaining data consistency.",
    learnings: "Mastered WebSocket implementation, database optimization, and complex state management patterns.",
    duration: "4 months",
    teamSize: "Solo project",
    year: "2024",
    deviceType: "mobile"
  },
  {
    id: 3,
    title: "Portfolio Website",
    description: "A performance-optimized showcase of full-stack development skills, demonstrating modern React architecture, advanced animations, and accessibility best practices.",
    longDescription: "This portfolio represents the culmination of my development skills, featuring a carefully architected React application with TypeScript for type safety and Tailwind CSS for consistent styling. I implemented advanced animations with Framer Motion, created an interactive terminal component, and optimized performance for lightning-fast load times. The site demonstrates modern development practices including component-based architecture, responsive design, accessibility compliance, and SEO optimization. Every interaction is carefully crafted to provide a smooth, engaging user experience while showcasing the depth of my technical capabilities.",
    image: "/api/placeholder/600/400",
    technologies: ["React", "TypeScript", "Tailwind CSS", "Vite", "Framer Motion"],
    category: "Frontend",
    type: "Portfolio",
    status: "Completed",
    featured: true,
    liveUrl: "https://chris-portfolio.vercel.app",
    githubUrl: "https://github.com/cjjutba/portfolio-v2",
    highlight: "Modern design with smooth animations and interactions",
    features: [
      "Responsive design system",
      "Smooth page transitions",
      "Interactive project showcases",
      "Dark/light theme support",
      "Performance optimized",
      "SEO friendly"
    ],
    challenges: "Creating smooth animations while maintaining performance across different devices and browsers.",
    learnings: "Advanced CSS techniques, animation principles, and performance optimization strategies.",
    duration: "2 months",
    teamSize: "Solo project",
    year: "2024",
    deviceType: "desktop"
  },
  {
    id: 4,
    title: "Weather Dashboard",
    description: "A comprehensive weather application with location-based forecasts, interactive maps, and detailed weather analytics built with modern React architecture.",
    longDescription: "WeatherPro delivers accurate weather information through a beautifully designed, intuitive interface. I integrated multiple weather APIs to provide comprehensive data including 7-day forecasts, interactive weather maps, severe weather alerts, and historical weather data visualization. The application features geolocation services, customizable dashboard widgets, and responsive design that works seamlessly across all devices. Advanced features include weather pattern analysis, personalized weather notifications, and offline data caching for improved user experience.",
    image: "/api/placeholder/600/400",
    technologies: ["React", "JavaScript", "Tailwind CSS", "Chart.js", "OpenWeather API", "Mapbox"],
    category: "Frontend",
    type: "Web Application",
    status: "Completed",
    featured: false,
    liveUrl: "https://weather-pro-demo.vercel.app",
    githubUrl: "https://github.com/cjjutba/weather-dashboard",
    highlight: "Interactive weather visualization with real-time data",
    features: [
      "7-day weather forecasts",
      "Interactive weather maps",
      "Severe weather alerts",
      "Historical data visualization",
      "Geolocation services",
      "Customizable dashboard widgets"
    ],
    challenges: "Integrating multiple weather APIs and handling real-time data updates efficiently.",
    learnings: "API integration, data visualization techniques, and geolocation services implementation.",
    duration: "1.5 months",
    teamSize: "Solo project",
    year: "2024",
    deviceType: "desktop"
  },
  {
    id: 5,
    title: "Social Media Dashboard",
    description: "A comprehensive analytics platform for social media management with real-time metrics, content scheduling, and performance tracking across multiple platforms.",
    longDescription: "SocialHub provides a unified dashboard for managing multiple social media accounts with advanced analytics and automation features. I built a robust backend system that integrates with various social media APIs to collect and analyze engagement data, track follower growth, and monitor content performance. The React frontend features interactive charts, customizable widgets, and an intuitive content scheduling system. Advanced features include sentiment analysis, competitor tracking, and automated reporting that helps users optimize their social media strategy.",
    image: "/api/placeholder/600/400",
    technologies: ["React", "Node.js", "Express.js", "MongoDB", "Chart.js", "Social Media APIs"],
    category: "Full-Stack",
    type: "Web Application",
    status: "Completed",
    featured: false,
    liveUrl: "https://socialhub-demo.vercel.app",
    githubUrl: "https://github.com/cjjutba/social-dashboard",
    highlight: "Multi-platform social media analytics and management",
    features: [
      "Multi-platform integration",
      "Real-time analytics dashboard",
      "Content scheduling system",
      "Performance tracking",
      "Sentiment analysis",
      "Automated reporting"
    ],
    challenges: "Managing multiple API integrations and handling large volumes of social media data efficiently.",
    learnings: "API rate limiting, data aggregation techniques, and building scalable analytics systems.",
    duration: "2.5 months",
    teamSize: "Solo project",
    year: "2024",
    deviceType: "desktop"
  },
  {
    id: 6,
    title: "Recipe Finder App",
    description: "A mobile-responsive recipe discovery platform with advanced search capabilities, meal planning features, and personalized recommendations.",
    longDescription: "RecipeHub transforms the cooking experience with intelligent recipe discovery and meal planning capabilities. I developed a comprehensive system that integrates with multiple recipe APIs to provide users with thousands of recipes, advanced filtering options, and personalized recommendations based on dietary preferences and cooking history. The application features a responsive design optimized for mobile use, offline recipe storage, shopping list generation, and social sharing capabilities. Advanced features include nutritional analysis, cooking timers, and step-by-step cooking guidance with voice commands.",
    image: "/api/placeholder/600/400",
    technologies: ["React", "TypeScript", "Tailwind CSS", "PWA", "Recipe APIs", "Local Storage"],
    category: "Frontend",
    type: "Web Application",
    status: "Completed",
    featured: false,
    liveUrl: "https://recipehub-demo.vercel.app",
    githubUrl: "https://github.com/cjjutba/recipe-finder",
    highlight: "Intelligent recipe discovery with personalized recommendations",
    features: [
      "Advanced recipe search and filtering",
      "Personalized recommendations",
      "Meal planning calendar",
      "Shopping list generation",
      "Offline recipe storage",
      "Nutritional analysis"
    ],
    challenges: "Implementing complex search algorithms and optimizing performance for mobile devices.",
    learnings: "PWA development, advanced search implementation, and mobile-first design principles.",
    duration: "2 months",
    teamSize: "Solo project",
    year: "2024",
    deviceType: "mobile"
  }
]

// Project Statistics and Counters
export const projectStats = {
  totalProjects: projects.length,
  featuredProjects: projects.filter(p => p.featured).length,
  completedProjects: projects.filter(p => p.status === "Completed").length,
  inProgressProjects: projects.filter(p => p.status === "In Progress").length,
  fullStackProjects: projects.filter(p => p.category === "Full-Stack").length,
  frontendProjects: projects.filter(p => p.category === "Frontend").length,
  backendProjects: projects.filter(p => p.category === "Backend").length,
  mobileProjects: projects.filter(p => p.category === "Mobile").length,
  currentYear: new Date().getFullYear(),
  projectsThisYear: projects.filter(p => p.year === "2024").length
}

// Featured projects for dashboard
export const featuredProjects = projects.filter(project => project.featured)

// Projects by category
export const projectsByCategory = {
  "Full-Stack": projects.filter(p => p.category === "Full-Stack"),
  "Frontend": projects.filter(p => p.category === "Frontend"),
  "Backend": projects.filter(p => p.category === "Backend"),
  "Mobile": projects.filter(p => p.category === "Mobile")
}

// Recent projects (last 3)
export const recentProjects = projects.slice(0, 3)

// Technologies used across all projects
export const allTechnologies = Array.from(
  new Set(projects.flatMap(project => project.technologies))
).sort()

// Project categories for filtering
export const projectCategories = [
  { name: "All", count: projects.length },
  { name: "Full-Stack", count: projectStats.fullStackProjects },
  { name: "Frontend", count: projectStats.frontendProjects },
  { name: "Backend", count: projectStats.backendProjects },
  { name: "Mobile", count: projectStats.mobileProjects }
]
