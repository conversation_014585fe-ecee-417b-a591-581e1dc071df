export const userData = {
  // Basic Information
  name: "<PERSON><PERSON>",
  title: "Full-Stack Developer",
  email: "<EMAIL>",
  phone: "+63 ************",
  location: "Plaridel, Misamis Occidental",
  
  // Bio variations for different contexts
  bios: {
    short: "Computer Engineer turned Full-Stack Developer. Building systems that scale.",
    medium: "Computer Engineering graduate specializing in full-stack development. I create seamless user experiences backed by robust, scalable architecture.",
    long: "I'm a Computer Engineering graduate who discovered my passion for full-stack development. What sets me apart is my systematic approach to building complete solutions—from database design and API architecture to responsive user interfaces and deployment strategies. I believe the best applications come from understanding the entire system, not just individual components. My engineering background taught me to think holistically about problems, and I apply this mindset to create seamless, scalable web applications that users love and businesses can rely on."
  },

  // Hero headlines for different contexts
  heroHeadlines: [
    "I build web applications that work",
    "Full-stack architect crafting end-to-end web experiences", 
    "From backend APIs to pixel-perfect frontends, I build it all",
    "Engineering complete solutions that users love and businesses trust"
  ],

  // Social Media Links
  social: {
    github: "https://github.com/cjjutba",
    linkedin: "https://linkedin.com/in/cjjutba", 
    twitter: "https://twitter.com/cjjutba",
    instagram: "https://instagram.com/cjjutba",
    portfolio: "https://cjjutba.com"
  },

  // Professional Statistics
  stats: {
    projectsCompleted: "15+",
    happyClients: "15+",
    fiveStarReviews: "12+",
    yearsExperience: "2+",
    technologiesUsed: "20+",
    linesOfCode: "50K+"
  },

  // Availability and Working Information
  availability: {
    status: "available", // "available", "busy", "unavailable"
    message: "Currently available for new projects",
    nextAvailable: "Immediate start"
  },

  workingHours: {
    timezone: "PHT (UTC+8)",
    days: "Monday - Friday", 
    hours: "9:00 AM - 6:00 PM",
    responseTime: {
      email: "Within 24 hours",
      phone: "Same day",
      meeting: "Within 48 hours"
    }
  },

  // Contact Inquiry Types
  inquiryTypes: [
    { value: "project", label: "Project Inquiry" },
    { value: "job", label: "Job Opportunity" },
    { value: "collaboration", label: "Collaboration" },
    { value: "general", label: "General Question" }
  ],

  // Education
  education: {
    degree: "Bachelor of Science in Computer Engineering",
    institution: "Jose Rizal Memorial State University",
    graduationYear: "2025",
    status: "Expected Graduate"
  },

  // Core Values and Approach
  values: [
    "End-to-end ownership of features",
    "Clean, maintainable code",
    "User-centered design thinking", 
    "Continuous learning and improvement",
    "Collaborative problem-solving"
  ],

  // Specializations
  specializations: [
    "Full-Stack Web Development",
    "React & Next.js Applications", 
    "Node.js & Express.js APIs",
    "Database Design & Management",
    "UI/UX Implementation",
    "Performance Optimization"
  ]
}
