@tailwind base;
@tailwind components;
@tailwind utilities;

/* CJ <PERSON>ba Portfolio Design System */

/* Hurme Geometric Sans Font Family */
@font-face {
  font-family: 'Hurme Geometric Sans';
  src: url('./assets/fonts/HurmeGeometricSans1-Hairline.otf') format('opentype');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Hurme Geometric Sans';
  src: url('./assets/fonts/HurmeGeometricSans1-HairlineOblique.otf') format('opentype');
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Hurme Geometric Sans';
  src: url('./assets/fonts/HurmeGeometricSans1-Thin.otf') format('opentype');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Hurme Geometric Sans';
  src: url('./assets/fonts/HurmeGeometricSans1-ThinOblique.otf') format('opentype');
  font-weight: 200;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Hurme Geometric Sans';
  src: url('./assets/fonts/HurmeGeometricSans1-Light.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Hurme Geometric Sans';
  src: url('./assets/fonts/HurmeGeometricSans1-LightOblique.otf') format('opentype');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Hurme Geometric Sans';
  src: url('./assets/fonts/HurmeGeometricSans1.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Hurme Geometric Sans';
  src: url('./assets/fonts/HurmeGeometricSans1-Oblique.otf') format('opentype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Hurme Geometric Sans';
  src: url('./assets/fonts/HurmeGeometricSans1-SemiBold.otf') format('opentype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Hurme Geometric Sans';
  src: url('./assets/fonts/HurmeGeometricSans1SemiBoldOblique.otf') format('opentype');
  font-weight: 600;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Hurme Geometric Sans';
  src: url('./assets/fonts/HurmeGeometricSans1-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Hurme Geometric Sans';
  src: url('./assets/fonts/HurmeGeometricSans1-BoldOblique.otf') format('opentype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Hurme Geometric Sans';
  src: url('./assets/fonts/HurmeGeometricSans1-Black.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Hurme Geometric Sans';
  src: url('./assets/fonts/HurmeGeometricSans1-BlackOblique.otf') format('opentype');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

/* Apparel Font Family for Special Styling */
@font-face {
  font-family: 'Apparel';
  src: url('./assets/fonts/apparel/Fontspring-DEMO-apparel-bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Apparel';
  src: url('./assets/fonts/apparel/Fontspring-DEMO-apparel-boldit.otf') format('opentype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Apparel';
  src: url('./assets/fonts/apparel/Fontspring-DEMO-apparel-black.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Apparel';
  src: url('./assets/fonts/apparel/Fontspring-DEMO-apparel-blackit.otf') format('opentype');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

@layer base {
  :root {
    /* Dark Mode Color Palette - Based on Aayush Bharti's Design */
    --background: 0 0% 7%;  /* Deep dark grey/near black #121212 */
    --foreground: 0 0% 100%;  /* Pure white for primary text */

    --card: 0 0% 14%;  /* Slightly lighter dark grey for containers #242424 */
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 7%;
    --popover-foreground: 0 0% 100%;

    /* Primary Purple/Magenta Colors */
    --primary: 285 85% 55%;  /* Deep Purple/Magenta #9438D5 */
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 14%;  /* Dark grey for secondary elements */
    --secondary-foreground: 0 0% 67%;  /* Light grey for secondary text #AAAAAA */

    --muted: 0 0% 14%;
    --muted-foreground: 0 0% 67%;

    --accent: 320 65% 60%;  /* Vibrant Pink/Magenta #DE55C8 */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 14%;  /* Dark grey borders */
    --input: 0 0% 14%;
    --ring: 285 85% 55%;

    --radius: 0.75rem;

    /* Portfolio specific colors - Dark Theme */
    --portfolio-accent: 285 85% 55%;
    --portfolio-accent-foreground: 0 0% 100%;
    --portfolio-background: 0 0% 7%;
    --portfolio-surface: 0 0% 14%;
    --portfolio-border: 0 0% 14%;

    /* Dark Theme Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(320 65% 60%), hsl(15 100% 70%));  /* Pink to Orange */
    --gradient-subtle: linear-gradient(135deg, hsl(0 0% 14%), hsl(0 0% 7%));

    /* Dark Shadows */
    --shadow-soft: 0 1px 3px 0 hsl(0 0% 0% / 0.3), 0 1px 2px -1px hsl(0 0% 0% / 0.3);
    --shadow-medium: 0 4px 6px -1px hsl(0 0% 0% / 0.3), 0 2px 4px -2px hsl(0 0% 0% / 0.3);
    --shadow-large: 0 20px 25px -5px hsl(0 0% 0% / 0.3), 0 8px 10px -6px hsl(0 0% 0% / 0.3);

    /* Dark mode scrollbar colors */
    --scrollbar-track: hsl(0 0% 7%);
    --scrollbar-thumb: hsl(0 0% 14%);
    --scrollbar-thumb-hover: hsl(285 85% 55%);
  }


}

/* Hurme Geometric Sans Typography Utilities */
@layer components {
  .font-hairline {
    font-weight: 100;
  }

  .font-thin {
    font-weight: 200;
  }

  .font-light {
    font-weight: 300;
  }

  .font-normal {
    font-weight: 400;
  }

  .font-semibold {
    font-weight: 600;
  }

  .font-bold {
    font-weight: 700;
  }

  .font-black {
    font-weight: 900;
  }

  /* Typography scale with Hurme Geometric Sans */
  .text-display {
    font-family: 'Hurme Geometric Sans', sans-serif;
    font-weight: 700;
    line-height: 1.1;
    letter-spacing: -0.02em;
  }

  .text-heading {
    font-family: 'Hurme Geometric Sans', sans-serif;
    font-weight: 600;
    line-height: 1.2;
    letter-spacing: -0.01em;
  }

  .text-subheading {
    font-family: 'Hurme Geometric Sans', sans-serif;
    font-weight: 500;
    line-height: 1.3;
  }

  .text-body {
    font-family: 'Hurme Geometric Sans', sans-serif;
    font-weight: 400;
    line-height: 1.6;
  }

  .text-caption {
    font-family: 'Hurme Geometric Sans', sans-serif;
    font-weight: 400;
    line-height: 1.4;
    font-size: 0.875rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Hurme Geometric Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    font-weight: 400;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    /* Premium dark gradient background with subtle noise texture */
    background:
      /* Fine grain noise texture for dark mode */
      radial-gradient(circle at 1px 1px, rgba(148, 56, 213, 0.03) 1px, transparent 0),
      /* Large-scale subtle gradient for dark mode */
      radial-gradient(ellipse 120% 80% at 50% 0%, rgba(148, 56, 213, 0.04) 0%, transparent 50%),
      radial-gradient(ellipse 100% 60% at 80% 100%, rgba(222, 85, 200, 0.03) 0%, transparent 50%),
      radial-gradient(ellipse 80% 100% at 20% 50%, rgba(255, 107, 107, 0.025) 0%, transparent 50%),
      /* Base dark background */
      hsl(var(--background));
    background-size:
      24px 24px,  /* Noise texture */
      100% 100%,  /* Gradients cover full area */
      100% 100%,
      100% 100%,
      100% 100%;
    background-attachment: fixed;
  }

  /* Premium Custom Scrollbar Styles */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--scrollbar-track);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: 4px;
    border: 1px solid transparent;
    background-clip: content-box;
    transition: all 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover);
    background-clip: content-box;
    transform: scale(1.1);
  }

  ::-webkit-scrollbar-corner {
    background: var(--scrollbar-track);
  }

  /* Enhanced scrollbar for main content areas */
  .scrollbar-enhanced::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  .scrollbar-enhanced::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--scrollbar-thumb), var(--scrollbar-thumb-hover));
    border-radius: 6px;
    border: 2px solid transparent;
    background-clip: content-box;
    box-shadow: inset 0 0 0 1px hsl(var(--border));
  }

  .scrollbar-enhanced::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-primary);
    box-shadow: inset 0 0 0 1px hsl(var(--primary));
  }

  /* Minimal scrollbar for sidebar and small containers */
  .scrollbar-minimal::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-minimal::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: 3px;
    opacity: 0.6;
    transition: opacity 0.2s ease;
  }

  .scrollbar-minimal::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover);
    opacity: 1;
  }

  .scrollbar-minimal::-webkit-scrollbar-track {
    background: transparent;
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hidden {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hidden::-webkit-scrollbar {
    display: none;
  }

  /* Firefox scrollbar styling */
  * {
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }

  .scrollbar-enhanced {
    scrollbar-width: auto;
    scrollbar-color: var(--scrollbar-thumb-hover) var(--scrollbar-track);
  }

  .scrollbar-minimal {
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) transparent;
  }

  .scrollbar-hidden {
    scrollbar-width: none;
  }
}

/* Additional utility classes for scrollbar management */
@layer utilities {
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  .scroll-auto {
    scroll-behavior: auto;
  }

  /* Custom Soft Shadow System */
  .shadow-soft-sm {
    box-shadow: 0 1px 2px 0 hsl(0 0% 0% / 0.05), 0 1px 3px 0 hsl(0 0% 0% / 0.1);
  }

  .shadow-soft {
    box-shadow: 0 1px 3px 0 hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  }

  .shadow-soft-md {
    box-shadow: 0 4px 6px -1px hsl(0 0% 0% / 0.1), 0 2px 4px -2px hsl(0 0% 0% / 0.1);
  }

  .shadow-soft-lg {
    box-shadow: 0 10px 15px -3px hsl(0 0% 0% / 0.1), 0 4px 6px -4px hsl(0 0% 0% / 0.1);
  }

  .shadow-soft-xl {
    box-shadow: 0 20px 25px -5px hsl(0 0% 0% / 0.1), 0 8px 10px -6px hsl(0 0% 0% / 0.1);
  }

  .shadow-soft-2xl {
    box-shadow: 0 25px 50px -12px hsl(0 0% 0% / 0.25);
  }

  /* Dark mode soft shadows */
  .dark .shadow-soft-sm {
    box-shadow: 0 1px 2px 0 hsl(0 0% 0% / 0.2), 0 1px 3px 0 hsl(0 0% 0% / 0.3);
  }

  .dark .shadow-soft {
    box-shadow: 0 1px 3px 0 hsl(0 0% 0% / 0.3), 0 1px 2px -1px hsl(0 0% 0% / 0.3);
  }

  .dark .shadow-soft-md {
    box-shadow: 0 4px 6px -1px hsl(0 0% 0% / 0.3), 0 2px 4px -2px hsl(0 0% 0% / 0.3);
  }

  .dark .shadow-soft-lg {
    box-shadow: 0 10px 15px -3px hsl(0 0% 0% / 0.3), 0 4px 6px -4px hsl(0 0% 0% / 0.3);
  }

  .dark .shadow-soft-xl {
    box-shadow: 0 20px 25px -5px hsl(0 0% 0% / 0.3), 0 8px 10px -6px hsl(0 0% 0% / 0.3);
  }

  .dark .shadow-soft-2xl {
    box-shadow: 0 25px 50px -12px hsl(0 0% 0% / 0.5);
  }

  /* Colored soft shadows for premium effects */
  .shadow-soft-primary {
    box-shadow: 0 10px 15px -3px hsl(217 91% 60% / 0.1), 0 4px 6px -4px hsl(217 91% 60% / 0.1);
  }

  .shadow-soft-primary-lg {
    box-shadow: 0 20px 25px -5px hsl(217 91% 60% / 0.15), 0 8px 10px -6px hsl(217 91% 60% / 0.1);
  }

  /* Micro-interactions with 0.3s transitions */
  .transition-micro {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-micro-fast {
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-micro-slow {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Terminal-specific scrollbar styles */
  .terminal-scrollbar-dark::-webkit-scrollbar {
    width: 8px;
  }

  .terminal-scrollbar-dark::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.3);
    border-radius: 4px;
  }

  .terminal-scrollbar-dark::-webkit-scrollbar-thumb {
    background: rgba(100, 116, 139, 0.5);
    border-radius: 4px;
    transition: background 0.3s ease;
  }

  .terminal-scrollbar-dark::-webkit-scrollbar-thumb:hover {
    background: rgba(100, 116, 139, 0.7);
  }

  .terminal-scrollbar-light::-webkit-scrollbar {
    width: 8px;
  }

  .terminal-scrollbar-light::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.5);
    border-radius: 4px;
  }

  .terminal-scrollbar-light::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.5);
    border-radius: 4px;
    transition: background 0.3s ease;
  }

  .terminal-scrollbar-light::-webkit-scrollbar-thumb:hover {
    background: rgba(148, 163, 184, 0.7);
  }

  /* Premium Sidebar Scrollbar Styles */
  .scrollbar-sidebar::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-sidebar::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-sidebar::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.3);
    border-radius: 3px;
    transition: all 0.3s ease;
  }

  .scrollbar-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(148, 163, 184, 0.6);
    transform: scaleX(1.2);
  }

  /* Dark mode sidebar scrollbar */
  .dark .scrollbar-sidebar::-webkit-scrollbar-thumb {
    background: rgba(100, 116, 139, 0.4);
  }

  .dark .scrollbar-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(100, 116, 139, 0.7);
  }
}