import {
  GraduationCap,
  Target,
  <PERSON>,
  <PERSON>rk<PERSON>,
  Rocket
} from "lucide-react"
import { TimelineItem, LearningResource } from './types'

// Timeline data
export const timelineData: TimelineItem[] = [
  {
    icon: GraduationCap,
    title: "Engineering Foundation",
    date: "May 2024",
    category: "Education",
    description: "Graduated with a B.S. in Computer Engineering. This systematic foundation taught me to think about problems holistically—from hardware constraints to software architecture—skills I now apply to full-stack development.",
    keyLearning: "Learned that the best solutions come from understanding the entire system, not just individual components.",
    link: null
  },
  {
    icon: Sparkles,
    title: "The Full-Stack Revelation",
    date: "June 2024",
    category: "Discovery",
    description: "Built my first complete web application with both frontend and backend. I realized that controlling the entire data flow—from database queries to user interactions—was where I could create the most value.",
    keyLearning: "Discovered that end-to-end ownership eliminates communication gaps and enables faster, more cohesive solutions.",
    link: null
  },
  {
    icon: Code,
    title: "First Production Application",
    date: "August 2024",
    category: "Milestone",
    description: "Deployed my first full-stack application using React, Node.js, and PostgreSQL. This project taught me the importance of considering database design, API architecture, and user experience as interconnected parts of a single system.",
    keyLearning: "Real-world applications require thinking beyond code—performance, security, and scalability must be built in from day one.",
    link: "https://github.com/cjutba"
  },
  {
    icon: Target,
    title: "TypeScript & System Design",
    date: "October 2024",
    category: "Growth",
    description: "Adopted TypeScript across my entire stack and began focusing on system architecture. Type safety from database to UI became my standard, dramatically improving code quality and development speed.",
    keyLearning: "Consistent typing across the full stack creates a seamless development experience and catches integration issues early.",
    link: null
  },
  {
    icon: Rocket,
    title: "Ready for Impact",
    date: "Present",
    category: "Future",
    description: "Seeking opportunities to apply my end-to-end development skills to solve real business problems. Whether building MVPs for startups or scaling applications for established companies, I'm ready to deliver complete solutions.",
    keyLearning: "The most valuable developers are those who can take ownership of entire features and deliver them from concept to production.",
    link: null
  }
]

// Learning resources data
export const learningResources: LearningResource[] = [
  {
    title: "Clean Code",
    author: "Robert C. Martin",
    type: "Book",
    takeaway: "This book taught me that the primary audience for my code is other developers.",
    link: "https://www.amazon.com/Clean-Code-Handbook-Software-Craftsmanship/dp/0132350882"
  },
  {
    title: "Epic React",
    author: "Kent C. Dodds",
    type: "Course",
    takeaway: "Deepened my understanding of the 'why' behind React's design patterns.",
    link: "https://epicreact.dev/"
  },
  {
    title: "CSS-Tricks",
    author: "Chris Coyier",
    type: "Blog",
    takeaway: "My go-to resource for creative solutions to complex styling challenges.",
    link: "https://css-tricks.com/"
  },
  {
    title: "JavaScript: The Good Parts",
    author: "Douglas Crockford",
    type: "Book",
    takeaway: "Helped me understand JavaScript's core strengths and how to write more effective code.",
    link: "https://www.amazon.com/JavaScript-Good-Parts-Douglas-Crockford/dp/0596517742"
  }
]
