// src/components/InteractiveTerminal.tsx

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'

interface InteractiveTerminalProps {
  className?: string
}

interface TerminalLine {
  id: string
  type: 'input' | 'output' | 'error'
  content: string
  timestamp: Date
}

export default function InteractiveTerminal({ className = '' }: InteractiveTerminalProps) {
  const [lines, setLines] = useState<TerminalLine[]>([])
  const [currentInput, setCurrentInput] = useState('')
  const [commandHistory, setCommandHistory] = useState<string[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)
  const terminalRef = useRef<HTMLDivElement>(null)
  const lineIdCounter = useRef(0)

  // NEW: Function to focus the input field
  const focusInput = () => {
    inputRef.current?.focus()
  }

  const getCurrentDate = () => {
    return new Date().toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  // Add line to terminal
  const addLine = useCallback((content: string, type: 'input' | 'output' | 'error' = 'output') => {
    const newLine: TerminalLine = {
      id: `line-${Date.now()}-${++lineIdCounter.current}`,
      type,
      content,
      timestamp: new Date()
    }
    setLines(prev => [...prev, newLine])
  }, [])

  // Clear terminal
  const clearTerminal = useCallback(() => {
    setLines([])
  }, [])

  // Command definitions
  const commands: Record<string, (args: string[]) => string | string[]> = {
    help: () => [
      '📋 Available Commands:',
      '',
      '  whois      - Display information about me',
      '  projects   - View my featured work',
      '  skills     - List my technical skills',
      '  contact    - Get my contact information',
      '  resume     - Download my resume',
      '  neofetch   - Display system information',
      '  clear      - Clear the terminal',
      '  history    - Show command history',
      '',
      '💡 Tip: Try typing \'sudo\' for a surprise!',
      '💡 Use ↑/↓ arrow keys to navigate command history'
    ],

    commands: () => [
      '📋 Available Commands:',
      '',
      '  help       - Show this help message',
      '  whois      - Display information about me',
      '  projects   - View my featured work',
      '  skills     - List my technical skills',
      '  contact    - Get my contact information',
      '  resume     - Download my resume',
      '  neofetch   - Display system information',
      '  clear      - Clear the terminal',
      '  history    - Show command history',
      '',
      '💡 Tip: Try typing \'sudo\' for a surprise!',
      '💡 Use ↑/↓ arrow keys to navigate command history'
    ],

    whois: () => [
      '// CJ JUTBA - FULL-STACK DEVELOPER',
      '',
      '🎓 Education: Computer Engineering Graduate (May 2024)',
      '💻 Specialization: Modern web development with React & TypeScript',
      '🚀 Passion: Building responsive, user-friendly applications',
      '🎯 Goal: Turning complex problems into elegant solutions',
      '',
      'Available for exciting frontend development opportunities.'
    ],

    projects: () => [
      'Featured Projects:',
      '',
      '01. E-commerce Platform',
      '    Full-stack retail app with React, Next.js & TypeScript',
      '    Status: Completed ✓',
      '',
      '02. Task Management App',
      '    Real-time collaborative workspace with Prisma & PostgreSQL',
      '    Status: In Progress ⚡',
      '',
      '03. Portfolio Website',
      '    Modern portfolio with interactive terminal (you\'re using it!)',
      '    Status: Completed ✓',
      '',
      '💡 Type \'contact\' to discuss potential collaborations!'
    ],

    skills: () => [
      'Technical Skills:',
      '',
      'Frontend:',
      '  React • TypeScript • Next.js • Tailwind CSS • JavaScript',
      '',
      'Backend:',
      '  Node.js • Express • Prisma • PostgreSQL',
      '',
      'Tools & Others:',
      '  Git • Vite • Supabase • Framer Motion • React Testing Library',
      '',
      '🌱 Currently learning: Advanced React patterns & performance optimization'
    ],

    contact: () => [
      'Let\'s Connect:',
      '',
      '📧 Email: <EMAIL>',
      '💼 LinkedIn: linkedin.com/in/cjjutba',
      '🐙 GitHub: github.com/cjjutba',
      '',
      'Open to discussing new opportunities and exciting projects!'
    ],

    resume: () => {
      // In a real implementation, you'd have an actual resume PDF
      window.open('/resume.pdf', '_blank')
      return [
        '📄 Opening resume in new tab...',
        '',
        'If the download didn\'t start, please contact me directly.'
      ]
    },

    neofetch: () => [
      'cj@portfolio',
      '─────────────────',
      `OS: Portfolio v2.0`,
      'Host: React + TypeScript',
      'Kernel: Vite 5.4.19',
      'Shell: Interactive Terminal',
      'Resolution: Responsive Design',
      'Theme: Purple Tech Professional',
      'CPU: Passion-driven Development',
      'Memory: Always Learning'
    ],

    sudo: () => [
      'sudo: cj is not in the sudoers file. This incident will be reported.',
      '',
      '😄 Nice try! But I\'m the only admin here.'
    ],

    clear: () => {
      clearTerminal()
      return []
    },

    history: () => commandHistory.length > 0 ? commandHistory.map((cmd, i) => `${i + 1}  ${cmd}`) : ['No commands in history']
  }

  // Execute command
  const executeCommand = useCallback((input: string) => {
    const trimmedInput = input.trim()
    if (!trimmedInput) return

    // Add input to history
    setCommandHistory(prev => [...prev, trimmedInput])
    setHistoryIndex(-1)

    // Add input line to terminal
    addLine(`<EMAIL>:~$ ${trimmedInput}`, 'input')

    // Parse command and arguments
    const [command, ...args] = trimmedInput.split(' ')

    // Execute command
    if (commands[command]) {
      const result = commands[command](args)
      if (Array.isArray(result)) {
        result.forEach(line => addLine(line, 'output'))
      } else if (result) {
        addLine(result, 'output')
      }
    } else {
      addLine(`Command not found: ${command}. Type 'help' for available commands.`, 'error')
    }
  }, [addLine, commands, commandHistory])

  // Handle key press
  const handleKeyPress = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      executeCommand(currentInput)
      setCurrentInput('')
    } else if (e.key === 'ArrowUp') {
      e.preventDefault()
      if (commandHistory.length > 0) {
        const newIndex = historyIndex === -1 ? commandHistory.length - 1 : Math.max(0, historyIndex - 1)
        setHistoryIndex(newIndex)
        setCurrentInput(commandHistory[newIndex])
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault()
      if (historyIndex !== -1) {
        const newIndex = historyIndex + 1
        if (newIndex >= commandHistory.length) {
          setHistoryIndex(-1)
          setCurrentInput('')
        } else {
          setHistoryIndex(newIndex)
          setCurrentInput(commandHistory[newIndex])
        }
      }
    }
  }, [currentInput, executeCommand, commandHistory, historyIndex])

  // Auto-focus input and scroll to bottom
  useEffect(() => {
    focusInput();
  }, [])

  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight
    }
  }, [lines])

  // Initialize with welcome message
  useEffect(() => {
    const welcomeLines = [
      'Welcome to CJ Jutba\'s Interactive Terminal',
      '==========================================',
      '',
      'Type \'help\' to see available commands.',
      'Use ↑/↓ arrow keys to navigate command history.',
      ''
    ]
    welcomeLines.forEach(line => addLine(line, 'output'))
  }, [addLine])



  // Interactive Terminal for all devices
  return (
    <motion.div
      className={`w-full ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.3 }}
    >
      {/* UPDATED: Enhanced inner glow effect */}
      <div className="rounded-2xl bg-neutral-900/50 backdrop-blur-lg shadow-[inset_0_0_35px_0_rgba(255,255,255,0.1)] overflow-hidden">
        {/* Window Chrome */}
        <div className="flex items-center justify-between px-4 py-3 bg-white/5">
          <div className="flex space-x-2">
            <div className="w-3 h-3 rounded-full bg-red-500 hover:bg-red-400 transition-colors cursor-pointer"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-500 hover:bg-yellow-400 transition-colors cursor-pointer"></div>
            <div className="w-3 h-3 rounded-full bg-green-500 hover:bg-green-400 transition-colors cursor-pointer"></div>
          </div>
          <p className="text-sm font-mono text-muted-foreground">
            cj@portfolio: ~
          </p>
          <div className="w-12"></div>
        </div>

        <div className="h-[400px] w-full overflow-hidden">
          <div
            ref={terminalRef}
            // UPDATED: Added onClick to focus the input field
            onClick={focusInput}
            className="h-full overflow-y-auto font-mono p-4 pb-8 text-sm leading-relaxed cursor-text"
          >
            {/* Terminal Output */}
            <div className="space-y-1">
              {lines.map((line) => (
                <div key={line.id} className={`${
                  line.type === 'input' ? 'text-primary font-bold' :
                  line.type === 'error' ? 'text-red-400' :
                  'text-foreground'
                }`}>
                  {line.content}
                </div>
              ))}
            </div>

            {/* Current Input Line */}
            <div className="flex items-center mt-2">
              <span className="text-primary font-bold mr-2"><EMAIL>:~$</span>
              <input
                ref={inputRef}
                type="text"
                value={currentInput}
                onChange={(e) => setCurrentInput(e.target.value)}
                onKeyDown={handleKeyPress}
                className="flex-1 bg-transparent border-none outline-none text-foreground font-mono"
                autoComplete="off"
                spellCheck={false}
                aria-label="Terminal command input"
                placeholder="Type a command..."
              />
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
