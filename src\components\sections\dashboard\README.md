# Dashboard Components

This directory contains all the modular components that make up the Dashboard page. Each component is self-contained and follows clean code principles.

## Structure

```
dashboard/
├── index.ts                    # Barrel exports for all components
├── types.ts                    # Shared TypeScript interfaces and animation variants
├── utils.ts                    # Shared utility functions
├── HeroSection.tsx             # Main hero section with terminal
├── FeaturedWorkSection.tsx     # Sticky scroll-driven project showcase
├── CoreTechnologiesSection.tsx # Technology skills grid
├── CurrentFocusSection.tsx     # Current projects and learning
├── JourneySummarySection.tsx   # Career journey timeline
├── CallToActionSection.tsx     # Final CTA section
└── README.md                   # This documentation
```

## Components

### HeroSection
- Main landing section with hero text and interactive terminal
- Includes navigation buttons and animated text
- Responsive grid layout

### FeaturedWorkSection
- Premium scroll-driven project showcase
- Sticky right column with project details
- Smooth animations and transitions

### CoreTechnologiesSection
- Grid display of core technologies
- Hover animations and icon rendering
- Link to full skills page

### CurrentFocusSection
- Two-column layout showing current projects and learning
- Card-based design with status badges
- Interactive hover effects

### JourneySummarySection
- Career timeline from graduation to present
- Clean card design with timeline visualization
- Link to full about page

### CallToActionSection
- Final section encouraging contact
- Gradient background and prominent buttons
- Multiple navigation options

## Shared Resources

### types.ts
- `BaseSectionProps`: Common interface for all sections
- `Skill`, `LearningItem`, `Project`: Data type definitions
- Animation variants for consistent motion design

### utils.ts
- `renderIcon()`: Safe icon rendering utility
- `createNavigationHandler()`: Navigation helper with scroll-to-top

## Usage

```tsx
import {
  HeroSection,
  FeaturedWorkSection,
  CoreTechnologiesSection,
  CurrentFocusSection,
  JourneySummarySection,
  CallToActionSection
} from "@/components/sections/dashboard"

export default function Dashboard() {
  return (
    <div className="container">
      <HeroSection />
      <FeaturedWorkSection />
      <CoreTechnologiesSection />
      <CurrentFocusSection />
      <JourneySummarySection />
      <CallToActionSection />
    </div>
  )
}
```

## Best Practices

1. **Modularity**: Each component is self-contained with its own logic
2. **Reusability**: Shared types and utilities prevent code duplication
3. **Consistency**: All components follow the same patterns and conventions
4. **Performance**: Components use proper React optimization techniques
5. **Accessibility**: All interactive elements have proper ARIA labels
6. **Responsive**: All components work across different screen sizes

## Dependencies

- React & React Hooks
- Framer Motion for animations
- React Router for navigation
- Lucide React & React Icons for icons
- Tailwind CSS for styling
- shadcn/ui components
