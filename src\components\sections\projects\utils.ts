import React from "react"
import { useNavigate } from "react-router-dom"
import { scrollToTopInstant } from "@/components/ScrollToTop"
import { Project, FilterCategory, TechIcon } from "./types"
import {
  Code,
  Palette,
  Database,
  Globe,
  Smartphone,
  Monitor,
  Tablet
} from "lucide-react"
import { SiReact, SiTypescript, SiNextdotjs, SiTailwindcss, SiJavascript, SiNodedotjs, SiMongodb, SiPostgresql, SiPrisma } from "react-icons/si"

// Tech icons mapping
export const techIcons: TechIcon = {
  "React": SiReact,
  "TypeScript": SiTypescript,
  "Next.js": SiNextdotjs,
  "Tailwind CSS": SiTailwindcss,
  "JavaScript": SiJavascript,
  "Node.js": SiNodedotjs,
  "MongoDB": SiMongodb,
  "PostgreSQL": SiPostgresql,
  "Prisma": SiPrisma,
  "Vite": Code,
  "Framer Motion": <PERSON>lette,
  "Socket.io": Database,
  "Stripe": Globe
}

// Helper function to render icons
export const renderIcon = (IconComponent: any, className: string) => {
  if (typeof IconComponent === 'function') {
    return React.createElement(IconComponent, { className })
  }
  return null
}

// Navigation helper
export const createNavigationHandler = () => {
  const navigate = useNavigate()
  
  return (path: string) => {
    scrollToTopInstant()
    navigate(path)
  }
}

// Filter projects by category
export const getProjectsByCategory = (projects: Project[], category: string) => {
  return category === "All" 
    ? projects 
    : projects.filter(project => project.category === category)
}

// Get filter categories with counts
export const getFilterCategories = (projects: Project[]): FilterCategory[] => {
  return [
    { name: "All", icon: Globe, count: projects.length },
    { name: "Frontend", icon: Monitor, count: projects.filter(p => p.category === "Frontend").length },
    { name: "Full-Stack", icon: Code, count: projects.filter(p => p.category === "Full-Stack").length },
    { name: "Mobile", icon: Smartphone, count: projects.filter(p => p.category === "Mobile").length },
    { name: "Design", icon: Palette, count: projects.filter(p => p.category === "Design").length }
  ]
}

// Get device icon based on type
export const getDeviceIcon = (deviceType: string) => {
  switch (deviceType) {
    case "mobile": return Smartphone
    case "tablet": return Tablet
    default: return Monitor
  }
}

// Get status color
export const getStatusColor = (status: string) => {
  switch (status) {
    case "Completed": return "bg-green-500/10 text-green-600 border-green-500/20"
    case "In Progress": return "bg-blue-500/10 text-blue-600 border-blue-500/20"
    case "Planning": return "bg-yellow-500/10 text-yellow-600 border-yellow-500/20"
    default: return "bg-gray-500/10 text-gray-600 border-gray-500/20"
  }
}
