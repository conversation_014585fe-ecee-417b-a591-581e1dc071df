import React from "react"
import { motion } from "framer-motion"
import { Github, Download, ExternalLink, Calendar, Coffee } from "lucide-react"
import { PrimaryButton, OutlineButton } from "@/components/ui/enhanced-button"
import { Card, CardContent } from "@/components/ui/card"
import Section from "@/components/Section"
import { BaseSectionProps, containerVariants, itemVariants } from "./types"
import { createNavigationHandler } from "./utils"

export const CallToActionSection: React.FC<BaseSectionProps> = ({ className }) => {
  const handleNavigation = createNavigationHandler()

  return (
    <Section
      title="Ready to Connect?"
      subtitle="Whether you're looking to hire me for a project, discuss a job opportunity, or just want to connect, let's start a conversation."
      className={className}
    >
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={containerVariants}
        className="space-y-8"
      >
        {/* Main CTA Card */}
        <motion.div variants={itemVariants}>
          <Card className="relative overflow-hidden border-border/50 bg-gradient-to-br from-card/90 via-card/95 to-background/90 backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10" />
            <CardContent className="relative p-8 md:p-12 text-center space-y-6">
              <motion.div
                variants={itemVariants}
                className="space-y-4"
              >
                <h3 className="text-2xl md:text-3xl font-bold text-foreground">
                  Let's Build Something Amazing Together
                </h3>
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                  I'm passionate about creating exceptional digital experiences and would love to help
                  bring your vision to life. Whether it's a new project, collaboration, or just a chat
                  about technology, I'm here to help.
                </p>
              </motion.div>

              <motion.div
                variants={itemVariants}
                className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-4"
              >
                <PrimaryButton
                  onClick={() => document.getElementById('contact-form')?.scrollIntoView({ behavior: 'smooth' })}
                  className="px-8 py-4"
                  arrowAnimation="slide"
                >
                  <Coffee className="mr-2 w-4 h-4" />
                  Let's Chat
                </PrimaryButton>
                <OutlineButton
                  onClick={() => window.open('https://calendly.com/cjjutba', '_blank')}
                  className="px-8 py-4"
                  showArrow={false}
                >
                  <Calendar className="mr-2 w-4 h-4" />
                  Schedule a Call
                </OutlineButton>
              </motion.div>

              <motion.div
                variants={itemVariants}
                className="pt-6 border-t border-border/50"
              >
                <p className="text-sm text-muted-foreground mb-4">
                  Want to learn more about my work and experience?
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
                  <OutlineButton
                    onClick={() => handleNavigation('/projects')}
                    className="px-6 py-2 text-sm"
                    showArrow={false}
                  >
                    <ExternalLink className="mr-2 w-3 h-3" />
                    View Portfolio
                  </OutlineButton>
                  <OutlineButton
                    onClick={() => window.open('https://github.com/cjjutba', '_blank')}
                    className="px-6 py-2 text-sm"
                    showArrow={false}
                  >
                    <Github className="mr-2 w-3 h-3" />
                    GitHub Profile
                  </OutlineButton>
                  <OutlineButton
                    onClick={() => window.open('/resume.pdf', '_blank')}
                    className="px-6 py-2 text-sm"
                    showArrow={false}
                  >
                    <Download className="mr-2 w-3 h-3" />
                    Download Resume
                  </OutlineButton>
                </div>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Quick Contact Options */}
        <motion.div
          variants={itemVariants}
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
        >
          <Card className="border-border/50 bg-card/90 backdrop-blur-sm hover:shadow-soft-xl transition-all duration-300 group cursor-pointer">
            <CardContent 
              className="p-6 text-center space-y-4"
              onClick={() => window.open('mailto:<EMAIL>', '_blank')}
            >
              <div className="w-12 h-12 bg-blue-500/10 rounded-lg mx-auto flex items-center justify-center group-hover:bg-blue-500/20 transition-colors duration-300">
                <ExternalLink className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h4 className="font-semibold text-foreground group-hover:text-blue-600 transition-colors duration-300">
                  Quick Email
                </h4>
                <p className="text-sm text-muted-foreground">
                  Send me a direct email
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="border-border/50 bg-card/90 backdrop-blur-sm hover:shadow-soft-xl transition-all duration-300 group cursor-pointer">
            <CardContent 
              className="p-6 text-center space-y-4"
              onClick={() => window.open('https://linkedin.com/in/cjjutba', '_blank')}
            >
              <div className="w-12 h-12 bg-blue-500/10 rounded-lg mx-auto flex items-center justify-center group-hover:bg-blue-500/20 transition-colors duration-300">
                <ExternalLink className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h4 className="font-semibold text-foreground group-hover:text-blue-600 transition-colors duration-300">
                  LinkedIn Connect
                </h4>
                <p className="text-sm text-muted-foreground">
                  Connect professionally
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="border-border/50 bg-card/90 backdrop-blur-sm hover:shadow-soft-xl transition-all duration-300 group cursor-pointer">
            <CardContent 
              className="p-6 text-center space-y-4"
              onClick={() => window.open('tel:+15551234567', '_blank')}
            >
              <div className="w-12 h-12 bg-green-500/10 rounded-lg mx-auto flex items-center justify-center group-hover:bg-green-500/20 transition-colors duration-300">
                <ExternalLink className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h4 className="font-semibold text-foreground group-hover:text-green-600 transition-colors duration-300">
                  Phone Call
                </h4>
                <p className="text-sm text-muted-foreground">
                  Call during business hours
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Fun Fact */}
        <motion.div
          variants={itemVariants}
          className="text-center"
        >
          <p className="text-sm text-muted-foreground">
            <strong className="text-foreground">Fun Fact:</strong> I typically respond to messages within{' '}
            <span className="text-primary font-semibold">24 hours</span> and love discussing new technologies,
            project ideas, and opportunities for collaboration!
          </p>
        </motion.div>
      </motion.div>
    </Section>
  )
}
