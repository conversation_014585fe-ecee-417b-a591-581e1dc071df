// src/components/sections/HeroSection.tsx

import React, { useState } from 'react';
import { motion, Variants } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Copy, Check, ArrowRight } from 'lucide-react';

import InteractiveTerminal from '@/components/InteractiveTerminal';
import { scrollToTopInstant } from '@/components/ScrollToTop';
import { Button } from '@/components/ui/button';
import { BaseSectionProps } from './types';
import { createNavigationHandler } from './utils';
import { userData } from '@/data/personal/userData';

// --- Animation Variants for a more dynamic entrance ---

const sentenceVariants: Variants = {
  hidden: { opacity: 1 },
  visible: {
    opacity: 1,
    transition: {
      delay: 0.2,
      staggerChildren: 0.08, // Time between each word animation
    },
  },
};

const wordVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: 'spring',
      damping: 12,
      stiffness: 100,
    },
  },
};

export default function HeroSection({ className }: BaseSectionProps) {
  const navigate = useNavigate();
  const handleNavigation = createNavigationHandler(navigate, scrollToTopInstant);
  const [emailCopied, setEmailCopied] = useState(false);

  const handleCopyEmail = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    if (navigator.clipboard) {
      navigator.clipboard.writeText(userData.email).then(() => {
        setEmailCopied(true);
        setTimeout(() => setEmailCopied(false), 2500);
      }).catch(err => console.error('Failed to copy email:', err));
    }
  };

  return (
    <motion.section
      className={`pt-12 md:pt-24 mb-40 md:mb-52 ${className || ''}`}
      initial="hidden"
      animate="visible"
      aria-labelledby="hero-heading"
    >
      {/* Reverted to a 50/50 grid layout for a balanced, clean look */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
        
        {/* --- Left Column: Content --- */}
        <div className="space-y-10 lg:space-y-12 order-1 lg:order-1">
          
          {/* Headline with Staggered Word Animation and two-line layout */}
          <motion.h1
            id="hero-heading"
            className="text-display text-4xl md:text-5xl lg:text-[3.5rem] xl:text-6xl font-bold text-foreground leading-tight [filter:drop-shadow(0_0_20px_rgba(255,255,255,0.2))]"
            variants={sentenceVariants}
          >
            <motion.span variants={wordVariants} className="inline-block mr-[0.25em]">I</motion.span>
            <motion.span variants={wordVariants} className="inline-block mr-[0.25em]">build</motion.span>
            <motion.span variants={wordVariants} className="inline-block">web</motion.span>
            <span className="block">
              <motion.span variants={wordVariants} className="inline-block mr-[0.25em]">apps</motion.span>
              <motion.span variants={wordVariants} className="inline-block">
                <span className="relative font-apparel font-black italic -skew-x-12 inline-block bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift">
                  that scale
                </span>
              </motion.span>
            </span>
          </motion.h1>

          {/* Bio Paragraph */}
          <motion.p
            className="text-body text-lg md:text-xl text-muted-foreground leading-relaxed max-w-2xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            {userData.bios.medium}
          </motion.p>
          
          {/* Enhanced CTA Section */}
          <motion.div
            className="flex flex-col sm:flex-row gap-6 items-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1 }}
          >
            {/* Primary CTA */}
            <Button
              onClick={() => handleNavigation('/contact')}
              size="lg"
              className="group w-full sm:w-auto"
            >
              Start a Conversation
              <ArrowRight className="w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" />
            </Button>

            {/* Secondary CTA (Subtle Copy Email) */}
            <motion.button
              type="button"
              onClick={handleCopyEmail}
              aria-label="Copy email address to clipboard"
              className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-all duration-200"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {emailCopied ? (
                <>
                  <Check className="w-4 h-4 text-green-500" />
                  <span className="text-green-400">Email Copied!</span>
                </>
              ) : (
                <>
                  <Copy className="w-4 h-4" />
                  <span>{userData.email}</span>
                </>
              )}
            </motion.button>
          </motion.div>
        </div>

        {/* --- Right Column: Interactive Terminal --- */}
        <div className="order-2 lg:order-2 mt-8 lg:mt-0">
          <motion.div
            className="will-change-transform" // Performance hint for the browser
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            // UPDATED: Using a spring animation for a more responsive and natural feel.
            // The 'delay' only applies to the initial animation, not the hover effect.
            transition={{ type: "spring", stiffness: 200, damping: 25, delay: 0.4 }}
            whileHover={{ 
              scale: 1.015, // Slightly increased scale for a more noticeable effect
              boxShadow: '0px 20px 40px -10px rgba(172, 102, 221, 0.2)' 
            }}
          >
            <InteractiveTerminal />
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}
