import { cn } from "@/lib/utils"

interface DeviceMockupProps {
  type: 'desktop' | 'mobile' | 'tablet'
  imageUrl?: string
  className?: string
  children?: React.ReactNode
}

export default function DeviceMockup({ type, imageUrl, className, children }: DeviceMockupProps) {
  if (type === 'desktop') {
    return (
      <div className={cn("relative", className)}>
        {/* Desktop Frame */}
        <div className="relative bg-secondary rounded-lg p-2 shadow-2xl">
          {/* Screen */}
          <div className="bg-card rounded-md overflow-hidden aspect-[16/10]">
            {imageUrl ? (
              <img
                src={imageUrl}
                alt="Project screenshot"
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-primary/5 to-primary/10 flex items-center justify-center">
                {children || (
                  <div className="text-muted-foreground text-sm">Project Preview</div>
                )}
              </div>
            )}
          </div>
          {/* Stand */}
          <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-16 h-4 bg-gray-700 rounded-b-lg"></div>
          <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 w-24 h-2 bg-gray-600 rounded-full"></div>
        </div>
      </div>
    )
  }

  if (type === 'mobile') {
    return (
      <div className={cn("relative", className)}>
        {/* Mobile Frame */}
        <div className="relative bg-secondary rounded-[2rem] p-2 shadow-2xl">
          {/* Screen */}
          <div className="bg-card rounded-[1.5rem] overflow-hidden aspect-[9/19.5]">
            {imageUrl ? (
              <img
                src={imageUrl}
                alt="Project screenshot"
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-primary/5 to-primary/10 flex items-center justify-center">
                {children || (
                  <div className="text-muted-foreground text-xs text-center px-4">Mobile Preview</div>
                )}
              </div>
            )}
          </div>
          {/* Home indicator */}
          <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-muted-foreground rounded-full"></div>
        </div>
      </div>
    )
  }

  if (type === 'tablet') {
    return (
      <div className={cn("relative", className)}>
        {/* Tablet Frame */}
        <div className="relative bg-secondary rounded-xl p-3 shadow-2xl">
          {/* Screen */}
          <div className="bg-card rounded-lg overflow-hidden aspect-[4/3]">
            {imageUrl ? (
              <img
                src={imageUrl}
                alt="Project screenshot"
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-primary/5 to-primary/10 flex items-center justify-center">
                {children || (
                  <div className="text-muted-foreground text-sm">Tablet Preview</div>
                )}
              </div>
            )}
          </div>
          {/* Home button */}
          <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-gray-700 rounded-full border-2 border-gray-600"></div>
        </div>
      </div>
    )
  }

  return null
}
