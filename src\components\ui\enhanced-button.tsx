import React from 'react'
import { motion } from 'framer-motion'
import { ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface EnhancedButtonProps {
  children: React.ReactNode
  onClick?: () => void
  variant?: 'primary' | 'outline'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  disabled?: boolean
  showArrow?: boolean
  arrowAnimation?: 'slide' | 'fade'
}

/**
 * Primary Button - Gradient background with shimmer effect
 * Used for main CTAs like "View My Work", "Contact Me"
 */
export const PrimaryButton: React.FC<EnhancedButtonProps> = ({
  children,
  onClick,
  size = 'lg',
  className,
  disabled = false,
  showArrow = true,
  arrowAnimation = 'slide'
}) => {
  return (
    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
      <Button
        onClick={onClick}
        disabled={disabled}
        size={size}
        className={cn(
          "group relative bg-gradient-to-r from-violet-600 via-purple-600 to-violet-700 hover:from-violet-700 hover:via-purple-700 hover:to-violet-800 text-white shadow-lg hover:shadow-2xl transition-all duration-300 ease-in-out px-8 py-4 text-base font-geometric font-semibold border-0 rounded-xl overflow-hidden",
          className
        )}
      >
        {/* Shimmer effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-in-out" />
        
        <span className="relative z-10 flex items-center">{children}</span>
        
        {showArrow && (
          <ArrowRight 
            className={cn(
              "relative z-10 ml-2 w-5 h-5 transition-transform duration-300 ease-in-out",
              arrowAnimation === 'slide' ? "group-hover:translate-x-1" : "",
              arrowAnimation === 'fade' ? "opacity-0 group-hover:opacity-100" : ""
            )} 
          />
        )}
      </Button>
    </motion.div>
  )
}

/**
 * Outline Button - Card background with subtle effects
 * Used for secondary CTAs like "About My Journey", "See all projects"
 */
export const OutlineButton: React.FC<EnhancedButtonProps> = ({
  children,
  onClick,
  size = 'lg',
  className,
  disabled = false,
  showArrow = true,
  arrowAnimation = 'fade'
}) => {
  return (
    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
      <Button
        onClick={onClick}
        disabled={disabled}
        variant="outline"
        size={size}
        className={cn(
          "group relative bg-card/90 hover:bg-card/95 border-2 border-border/80 hover:border-primary/60 text-foreground hover:text-primary px-8 py-4 text-base font-geometric font-semibold transition-all duration-300 ease-in-out rounded-xl shadow-sm hover:shadow-lg backdrop-blur-sm overflow-hidden",
          className
        )}
      >
        {/* Subtle shimmer effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/10 to-primary/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-in-out" />
        
        <span className="relative z-10 flex items-center">{children}</span>
        
        {showArrow && (
          <ArrowRight 
            className={cn(
              "relative z-10 ml-2 w-4 h-4 transition-transform duration-300 ease-in-out",
              arrowAnimation === 'slide' ? "group-hover:translate-x-0.5" : "",
              arrowAnimation === 'fade' ? "opacity-0 group-hover:opacity-100" : ""
            )} 
          />
        )}
      </Button>
    </motion.div>
  )
}

/**
 * Card Button - For project cards and similar components
 * Used for "Live Demo", "View Code" buttons
 */
export const CardButton: React.FC<EnhancedButtonProps> = ({
  children,
  onClick,
  variant = 'outline',
  size = 'sm',
  className,
  disabled = false,
  showArrow = true
}) => {
  const baseClasses = variant === 'primary'
    ? "flex-1 group/btn bg-secondary hover:bg-primary/10 hover:text-primary text-muted-foreground transition-all duration-300 ease-in-out border border-border hover:border-primary/50 rounded-lg hover:shadow-sm text-sm font-geometric"
    : "flex-1 group/btn bg-card hover:bg-secondary hover:text-foreground text-muted-foreground transition-all duration-300 ease-in-out border border-border hover:border-border rounded-lg hover:shadow-sm text-sm font-geometric"

  return (
    <Button
      onClick={onClick}
      disabled={disabled}
      variant="ghost"
      className={cn(baseClasses, className)}
    >
      {children}
      {showArrow && (
        <ArrowRight className="ml-2 w-4 h-4 group-hover/btn:translate-x-0.5 transition-transform duration-300 ease-in-out" />
      )}
    </Button>
  )
}
