import React from 'react'
import { motion } from 'framer-motion'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Clock, BookOpen } from 'lucide-react'
import { cn } from '@/lib/utils'
import { BaseSectionProps, staggerContainerVariants, itemVariants } from './types'
import { currentlyLearning } from '@/data/skills/skillsData'
import { renderIcon } from './utils'

export default function LearningGoalsSection({ className }: BaseSectionProps) {
  return (
    <section className={cn('py-24 sm:py-32', className)}>
      {/* Section Header */}
      <div className="mb-20 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-2"
        >
          <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
            MY ONGOING QUEST FOR KNOWLEDGE
          </span>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight"
        >
          Learning{' '}
          <span className="relative font-apparel font-black italic -skew-x-12 inline-block bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift [filter:drop-shadow(0_0_15px_rgba(148,56,213,0.5))_drop-shadow(0_0_1.5px_rgba(0,0,0,0.2))]">
            Goals
          </span>
        </motion.h2>
      </div>

      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={staggerContainerVariants}
        className="max-w-6xl mx-auto"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {currentlyLearning.map((goal) => (
            <motion.div key={goal.name} variants={itemVariants}>
              <Card className={cn(
                'group h-full transition-all duration-300 transform hover:scale-[1.02]',
                'bg-neutral-900/50 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] rounded-2xl border-transparent'
              )}>
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-lg bg-neutral-800/60 flex items-center justify-center">
                        {renderIcon(goal.icon, "w-5 h-5 text-primary")}
                      </div>
                      <div>
                        <CardTitle className="text-heading text-lg group-hover:text-primary transition-colors duration-300">
                          {goal.name}
                        </CardTitle>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Progress</span>
                      <Badge variant="outline" className="font-bold text-primary">
                        {goal.progress}%
                      </Badge>
                    </div>
                    <Progress value={goal.progress} className="h-2" />
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <CardDescription className="text-sm leading-relaxed">
                    {goal.description}
                  </CardDescription>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>{goal.estimatedCompletion}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <BookOpen className="w-4 h-4 text-primary" />
                      <span className="text-sm font-medium text-primary">Learning Resources:</span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {goal.resources.map((resource, idx) => (
                        <Badge
                          key={idx}
                          variant="outline"
                          className="text-xs px-2 py-1"
                        >
                          {resource}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </section>
  )
}
