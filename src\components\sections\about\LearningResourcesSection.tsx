import React from 'react'
import { motion } from 'framer-motion'
import { Star, ExternalLink } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { OutlineButton } from '@/components/ui/enhanced-button'
import { cn } from "@/lib/utils"
import { BaseSectionProps, staggerContainerVariants, itemVariants } from './types'
import { learningResources } from './data'

export default function LearningResourcesSection({ className }: BaseSectionProps) {
  return (
    <section className={cn('py-24 sm:py-32', className)}>
      {/* Section Header */}
      <div className="mb-20 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-2"
        >
          <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
            THE TOOLS & TUTORIALS THAT FUEL MY KNOWLEDGE
          </span>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight"
        >
          My Digital{' '}
          <span className="relative font-apparel font-black italic -skew-x-12 inline-block bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift [filter:drop-shadow(0_0_15px_rgba(148,56,213,0.5))_drop-shadow(0_0_1.5px_rgba(0,0,0,0.2))]">
            Bookshelf
          </span>
        </motion.h2>
      </div>

      {/* Main Content */}
      <div className="bg-transparent">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={staggerContainerVariants}
          className="max-w-4xl mx-auto"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {learningResources.map((resource) => (
              <motion.div key={resource.title} variants={itemVariants}>
                <Card className={cn(
                  'group h-full transition-all duration-300 transform hover:scale-[1.02] cursor-pointer',
                  'bg-neutral-900/50 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] rounded-2xl border-transparent'
                )}>
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-heading text-lg group-hover:text-primary transition-colors duration-300">
                          {resource.title}
                        </CardTitle>
                        <CardDescription className="mt-1">
                          by {resource.author}
                        </CardDescription>
                      </div>
                      <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                        {resource.type}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-start gap-2">
                      <Star className="w-4 h-4 text-primary flex-shrink-0 mt-0.5" />
                      <p className="text-sm text-muted-foreground italic">
                        "{resource.takeaway}"
                      </p>
                    </div>
                    <OutlineButton
                      className="w-full text-sm px-4 py-2"
                      onClick={() => window.open(resource.link, '_blank')}
                      showArrow={false}
                    >
                      <ExternalLink className="w-3 h-3 mr-2" />
                      View Resource
                    </OutlineButton>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
