import React from 'react'
import { NavigateFunction } from 'react-router-dom'
import { TechnicalSkill } from './types'

// Helper function to render icons safely
export const renderIcon = (IconComponent: any, className: string) => {
  if (typeof IconComponent === 'function') {
    return React.createElement(IconComponent, { className })
  }
  return null
}

// Enhanced navigation function that scrolls to top before navigating
export const createNavigationHandler = (
  navigate: NavigateFunction,
  scrollToTopInstant: () => void
) => {
  return (path: string) => {
    scrollToTopInstant()
    navigate(path)
  }
}

// Get skills by category - updated for new category mappings
export const getSkillsByCategory = (skills: TechnicalSkill[], category: string) => {
  if (category === "All") return skills

  switch (category) {
    case "Frontend":
      return skills.filter(skill =>
        skill.category === "Frontend" ||
        skill.category === "Styling" ||
        skill.category === "Animation"
      )
    case "Backend":
      return skills.filter(skill => skill.category === "Backend")
    case "Database & DevOps":
      return skills.filter(skill =>
        skill.category === "Database" ||
        skill.category === "Tools"
      )
    case "Testing":
      return skills.filter(skill => skill.category === "Testing")
    default:
      return skills.filter(skill => skill.category === category)
  }
}

// Get skill level color
export const getSkillLevelColor = (level: number) => {
  if (level >= 90) return "text-green-600 dark:text-green-400"
  if (level >= 80) return "text-blue-600 dark:text-blue-400"
  if (level >= 70) return "text-yellow-600 dark:text-yellow-400"
  return "text-orange-600 dark:text-orange-400"
}

// Get skill level background
export const getSkillLevelBg = (level: number) => {
  if (level >= 90) return "bg-green-500/10 border-green-500/20"
  if (level >= 80) return "bg-blue-500/10 border-blue-500/20"
  if (level >= 70) return "bg-yellow-500/10 border-yellow-500/20"
  return "bg-orange-500/10 border-orange-500/20"
}

// Get priority color
export const getPriorityColor = (priority: string) => {
  switch (priority) {
    case "High": return "bg-red-500/10 text-red-600 border-red-500/20"
    case "Medium": return "bg-yellow-500/10 text-yellow-600 border-yellow-500/20"
    case "Low": return "bg-green-500/10 text-green-600 border-green-500/20"
    default: return "bg-gray-500/10 text-gray-600 border-gray-500/20"
  }
}
