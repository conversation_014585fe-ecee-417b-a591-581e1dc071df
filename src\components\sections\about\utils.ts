import React from 'react'
import { NavigateFunction } from 'react-router-dom'

// Helper function to render icons safely
export const renderIcon = (IconComponent: any, className: string) => {
  if (typeof IconComponent === 'function') {
    return React.createElement(IconComponent, { className })
  }
  return null
}

// Enhanced navigation function that scrolls to top before navigating
export const createNavigationHandler = (
  navigate: NavigateFunction,
  scrollToTopInstant: () => void
) => {
  return (path: string) => {
    scrollToTopInstant()
    navigate(path)
  }
}
