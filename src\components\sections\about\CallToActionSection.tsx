import React from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent } from '@/components/ui/card'
import { PrimaryButton, OutlineButton } from '@/components/ui/enhanced-button'
import Section from '@/components/Section'
import { scrollToTopInstant } from '@/components/ScrollToTop'
import { BaseSectionProps, itemVariants } from './types'
import { createNavigationHandler } from './utils'

export default function CallToActionSection({ className }: BaseSectionProps) {
  const navigate = useNavigate()
  const handleNavigation = createNavigationHandler(navigate, scrollToTopInstant)

  return (
    <Section animate={false} className={className}>
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={itemVariants}
      >
        <Card className="border-border bg-card hover:shadow-lg transition-shadow duration-200">
          <CardContent className="p-12 text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h2 className="text-display text-3xl md:text-4xl lg:text-5xl text-foreground">
                  Let's Build Something Amazing Together
                </h2>
                <p className="text-body text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto mt-6">
                  I'm always excited to discuss new opportunities, collaborate on interesting projects, or simply connect with fellow developers. Let's create something meaningful together.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <PrimaryButton
                  onClick={() => handleNavigation('/contact')}
                  className="px-8 py-3"
                  arrowAnimation="slide"
                >
                  Get In Touch
                </PrimaryButton>
                <OutlineButton
                  onClick={() => handleNavigation('/projects')}
                  className="px-8 py-3"
                >
                  View My Projects
                </OutlineButton>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </Section>
  )
}
