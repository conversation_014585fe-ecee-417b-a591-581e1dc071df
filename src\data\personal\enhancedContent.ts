export const enhancedContent = {
  // About section enhanced content
  about: {
    title: "From Engineering to End-to-End Development",
    narrative: "My journey began with a Computer Engineering degree, where I learned to think systematically about complex problems. But it was when I built my first complete web application—handling everything from database design to user interface—that I discovered my true passion. I realized that the most elegant solutions come from understanding the entire system, not just individual components. Today, I specialize in full-stack development because I believe the best applications are built by developers who can see the big picture and own the complete user experience from database to deployment.",
    
    keyPoints: [
      "End-to-end ownership eliminates communication gaps",
      "Systematic engineering approach to problem-solving", 
      "Focus on building complete, scalable solutions",
      "Understanding the entire application lifecycle"
    ],
    
    philosophy: "I don't just write code—I architect solutions. Every line of code I write is informed by how it fits into the larger system, from database performance to user experience."
  },

  // Contact section enhanced content
  contact: {
    headlines: [
      "Let's Build Something Great Together",
      "Have a Project in Mind?",
      "Ready to Start Your Next Project?", 
      "Get In Touch",
      "Let's Create Something Amazing"
    ],
    
    introText: "Whether you're a startup looking to build your MVP, an established company needing a full-stack developer, or an agency seeking a reliable partner, I'm here to help bring your vision to life. I'm currently available for both full-time opportunities and freelance projects. Let's discuss how we can work together to create something exceptional.",
    
    callToActions: {
      fullTime: "Explore Full-Time Opportunities",
      freelance: "Discuss Your Project",
      general: "Start a Conversation"
    },
    
    availability: {
      status: "Available",
      message: "Currently accepting new projects and opportunities",
      responseTime: "I typically respond within 24 hours"
    }
  },

  // Skills section enhanced content
  skills: {
    introduction: "My comprehensive toolkit for building, testing, and deploying modern web applications. Each skill has been carefully developed to work in harmony with the others, creating a seamless development workflow from initial concept to production deployment.",
    
    categories: {
      frontend: {
        title: "Frontend Development",
        description: "Creating responsive, interactive user interfaces with modern frameworks and libraries"
      },
      backend: {
        title: "Backend Development", 
        description: "Building robust APIs, managing databases, and handling server-side logic"
      },
      database: {
        title: "Database & DevOps",
        description: "Designing efficient data structures and managing deployment pipelines"
      },
      testing: {
        title: "Testing & Quality Assurance",
        description: "Ensuring code quality through comprehensive testing strategies"
      }
    },
    
    philosophy: "I believe in continuous learning and staying current with industry best practices. My skill set is constantly evolving to meet the demands of modern web development."
  },

  // Projects section enhanced content
  projects: {
    introduction: "Each project represents a complete solution built from the ground up, showcasing my ability to handle every aspect of the development process from initial planning to final deployment.",
    
    approach: "I approach every project with a systematic methodology: understanding the problem, architecting the solution, implementing with best practices, and delivering a polished product that exceeds expectations.",
    
    categories: {
      featured: "Featured Work",
      fullStack: "Full-Stack Applications", 
      frontend: "Frontend Showcases",
      backend: "Backend Systems"
    }
  },

  // Hero section variations
  hero: {
    headlines: [
      "I build complete digital solutions from database to deployment",
      "Full-stack architect crafting end-to-end web experiences",
      "From backend APIs to pixel-perfect frontends, I build it all", 
      "Engineering complete solutions that users love and businesses trust",
      "I transform ideas into full-stack applications that scale"
    ],
    
    subheadlines: [
      "Computer Engineering graduate specializing in modern web development",
      "Combining systematic thinking with creative problem-solving",
      "Building scalable applications with cutting-edge technologies"
    ]
  },

  // Value propositions for different audiences
  valuePropositions: {
    forEmployers: [
      "End-to-end development capabilities reduce team coordination overhead",
      "Engineering background ensures systematic, scalable solutions",
      "Proven ability to own complete features from concept to production",
      "Strong foundation in both technical skills and professional competencies"
    ],
    
    forClients: [
      "Complete solution provider - no need for multiple developers",
      "Faster development cycles through integrated approach", 
      "Reliable partner who understands business requirements",
      "Proven track record with measurable results"
    ]
  },

  // Testimonials and social proof
  socialProof: {
    testimonials: [
      {
        text: "CJ delivered exactly what we needed - a complete solution that just works.",
        author: "Previous Client",
        role: "Startup Founder"
      }
    ],
    
    achievements: [
      "15+ successful projects delivered",
      "100% client satisfaction rate",
      "2+ years of development experience",
      "Expertise across the full technology stack"
    ]
  }
}
