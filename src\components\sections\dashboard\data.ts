import { CheckSquare, Code, Folder, BookOpen, Rocket, ShoppingCart, Sparkles } from 'lucide-react'
import { SiReact, SiTypescript, SiNextdotjs, SiTailwindcss, SiJavascript, SiGit } from 'react-icons/si'
import { Skill, LearningItem, Project } from './types'

// Core technologies data
export const coreSkills: Skill[] = [
  { name: "React", category: "Frontend", icon: SiReact },
  { name: "TypeScript", category: "Frontend", icon: SiTypescript },
  { name: "Next.js", category: "Frontend", icon: SiNextdotjs },
  { name: "Tailwind CSS", category: "Styling", icon: SiTailwindcss },
  { name: "JavaScript", category: "Frontend", icon: SiJavascript },
  { name: "Git", category: "Tools", icon: SiGit }
]

// Current learning data
export const currentlyLearning: LearningItem[] = [
  { name: "Advanced React Patterns", icon: Code },
  { name: "TypeScript Best Practices", icon: BookOpen },
  { name: "Frontend Performance", icon: Rocket },
  { name: "UI/UX Design Principles", icon: Sparkles }
]

// Current projects data
export const workingOn: Project[] = [
  { name: "Personal Portfolio Website", status: "Active", icon: Folder },
  { name: "E-commerce Platform", status: "Planning", icon: ShoppingCart },
  { name: "Task Management App", status: "Planning", icon: CheckSquare }
]
