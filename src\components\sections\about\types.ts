import { LucideIcon } from 'lucide-react'

// Base interface for all about sections
export interface BaseSectionProps {
  className?: string
}

// Timeline item interface
export interface TimelineItem {
  icon: LucideIcon
  title: string
  date: string
  category: string
  description: string
  keyLearning: string
  link: string | null
}

// Learning resource interface
export interface LearningResource {
  title: string
  author: string
  type: string
  takeaway: string
  link: string
}

// Animation variants
export const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
}

export const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  }
}

export const staggerContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
      delayChildren: 0.1
    }
  }
}
