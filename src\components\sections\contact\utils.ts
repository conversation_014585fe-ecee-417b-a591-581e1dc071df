import { useNavigate } from "react-router-dom"
import { scrollToTopInstant } from "@/components/ScrollToTop"
import { FormData, FormErrors } from "./types"

// Navigation helper
export const createNavigationHandler = () => {
  const navigate = useNavigate()

  return (path: string) => {
    scrollToTopInstant()
    navigate(path)
  }
}

// Form validation
export const validateForm = (formData: FormData): FormErrors => {
  const errors: FormErrors = {}

  if (!formData.name.trim()) {
    errors.name = "Name is required"
  }

  if (!formData.email.trim()) {
    errors.email = "Email is required"
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.email = "Please enter a valid email address"
  }

  if (!formData.subject.trim()) {
    errors.subject = "Subject is required"
  }

  if (!formData.message.trim()) {
    errors.message = "Message is required"
  } else if (formData.message.trim().length < 10) {
    errors.message = "Message must be at least 10 characters long"
  }

  return errors
}

// Form submission handler
export const handleFormSubmit = async (
  formData: FormData,
  setIsSubmitting: (value: boolean) => void,
  setIsSubmitted: (value: boolean) => void
) => {
  setIsSubmitting(true)
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Here you would typically send the form data to your backend
    console.log("Form submitted:", formData)
    
    setIsSubmitted(true)
  } catch (error) {
    console.error("Form submission error:", error)
  } finally {
    setIsSubmitting(false)
  }
}

// Email validation helper
export const isValidEmail = (email: string): boolean => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
}

// Format phone number
export const formatPhoneNumber = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '')
  const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/)
  if (match) {
    return `(${match[1]}) ${match[2]}-${match[3]}`
  }
  return phone
}

// Get inquiry type color
export const getInquiryTypeColor = (type: string): string => {
  switch (type) {
    case "project":
      return "bg-blue-500/10 text-blue-600 border-blue-500/20"
    case "job":
      return "bg-green-500/10 text-green-600 border-green-500/20"
    case "collaboration":
      return "bg-purple-500/10 text-purple-600 border-purple-500/20"
    case "general":
      return "bg-gray-500/10 text-gray-600 border-gray-500/20"
    default:
      return "bg-primary/10 text-primary border-primary/20"
  }
}
