import { useState, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Sidebar } from "@/components/Sidebar";
import { FloatingNav } from "@/components/FloatingNav";
import ScrollToTop from "@/components/ScrollToTop";
import SpeedDialFAB from "@/components/SpeedDialFAB";
import Dashboard from "./pages/Dashboard";
import About from "./pages/About";
import Skills from "./pages/Skills";
import Projects from "./pages/Projects";
import Contact from "./pages/Contact";
import NotFound from "./pages/NotFound";
import { cn } from "@/lib/utils";

const queryClient = new QueryClient();

const App = () => {
  const [sidebarExpanded, setSidebarExpanded] = useState(true);

  // Listen for sidebar state changes from localStorage
  useEffect(() => {
    const handleStorageChange = () => {
      const savedState = localStorage.getItem('sidebarState');
      setSidebarExpanded(savedState !== 'collapsed');
    };

    // Initial load
    handleStorageChange();

    // Listen for changes from other tabs
    window.addEventListener('storage', handleStorageChange);

    // Custom event for same-tab changes
    const handleSidebarToggle = () => handleStorageChange();
    window.addEventListener('sidebarToggle', handleSidebarToggle);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('sidebarToggle', handleSidebarToggle);
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <ScrollToTop />
          <div className="min-h-screen bg-background">
            {/* Layout: Sidebar (fixed) + Main Content (centered) + FloatingNav (fixed) */}
            <div className="relative min-h-screen">
              {/* Sidebar - Fixed positioned */}
              <Sidebar />

              {/* Main Content - Adjusted margins for better visual centering */}
              <main className={cn(
                "min-h-screen pb-16 lg:pb-0 transition-all duration-300 ease-in-out",
                // The left margin is necessary to clear the wide sidebar
                sidebarExpanded ? "lg:ml-80" : "lg:ml-20",
                // UPDATED: Reduced the right margin to better balance the layout against the slim floating nav
                "lg:mr-24" // Was lg:mr-32
              )}>
                {/* Content container with max-width and proper centering within the main area */}
                <div className="w-full max-w-6xl mx-auto px-6 lg:px-12 xl:px-16">
                  <Routes>
                    <Route path="/" element={<Dashboard />} />
                    <Route path="/about" element={<About />} />
                    <Route path="/skills" element={<Skills />} />
                    <Route path="/projects" element={<Projects />} />
                    <Route path="/contact" element={<Contact />} />
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </div>
              </main>

              {/* FloatingNav - Fixed positioned */}
              <FloatingNav />
            </div>

            <SpeedDialFAB />
          </div>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
