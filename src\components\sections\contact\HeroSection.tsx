import React from "react"
import { motion } from "framer-motion"
import { PrimaryButton, OutlineButton } from "@/components/ui/enhanced-button"
import { BaseSectionProps, containerVariants, itemVariants } from "./types"
import { contactStats } from "./data"
import { createNavigationHandler } from "./utils"
import { userData } from "@/data/personal/userData"
import { enhancedContent } from "@/data/personal/enhancedContent"

export const HeroSection: React.FC<BaseSectionProps> = ({ className }) => {
  const handleNavigation = createNavigationHandler()

  return (
    <motion.div
      className={`mb-24 md:mb-32 ${className || ""}`}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="text-center space-y-8">
        <motion.div
          className="space-y-6"
          variants={itemVariants}
        >
          <motion.h1
            className="text-display text-4xl md:text-5xl lg:text-6xl text-foreground"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.05 }}
          >
            {enhancedContent.contact.headlines[0].split(' ').slice(0, -2).join(' ')}{' '}
            <span className="text-primary bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
              Great Together
            </span>
          </motion.h1>

          <motion.p
            className="text-lg md:text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            {enhancedContent.contact.introText}
          </motion.p>
        </motion.div>

        <motion.div
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.15 }}
        >
          <PrimaryButton
            onClick={() => document.getElementById('contact-form')?.scrollIntoView({ behavior: 'smooth' })}
            className="px-8 py-4"
            arrowAnimation="slide"
          >
            Start a Project
          </PrimaryButton>
          <OutlineButton
            onClick={() => handleNavigation('/projects')}
            className="px-8 py-4"
            showArrow={false}
          >
            View My Work
          </OutlineButton>
        </motion.div>

        {/* Contact Statistics */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-6 pt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        >
          {contactStats.map((stat, index) => (
            <motion.div
              key={stat.label}
              className="text-center space-y-2"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.25 + index * 0.05 }}
            >
              <div className="w-12 h-12 bg-primary/10 dark:bg-primary/20 rounded-xl mx-auto flex items-center justify-center mb-3">
                <stat.icon className="w-6 h-6 text-primary" />
              </div>
              <div className="text-2xl md:text-3xl font-bold text-foreground">
                {stat.value}
              </div>
              <div className="text-sm text-muted-foreground">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </motion.div>
  )
}
