import {
  <PERSON>,
  Palette,
  Wrench,
  <PERSON>,
  Book<PERSON>pen,
  Star,
  TrendingUp,
  Award,
  Target,
  Zap,
  Brain,
  Lightbulb,
  Rocket,
  Calendar,
  Clock,
  CheckCircle,
  Circle,
  Database,
  Globe,
  Smartphone,
  Monitor,
  GitBranch,
  Settings,
  Layers,
  PenTool,
  Coffee,
  Heart,
  Eye,
  MessageSquare,
  Users2,
  Timer,
  Focus,
  Repeat,
  <PERSON>rkles
} from "lucide-react"
import {
  SiReact,
  SiTypescript,
  SiNextdotjs,
  SiTailwindcss,
  SiJavascript,
  SiGit,
  SiHtml5,
  SiCss3,
  SiNodedotjs,
  SiPostgresql,
  SiPrisma,
  SiFigma,
  SiSass,
  SiStyledcomponents,
  SiFramer,
  SiVitest,
  SiGraphql,
  SiVuedotjs
} from "react-icons/si"
import { TechnicalSkill, SoftSkill, LearningGoal, CategoryFilter } from './types'

// Enhanced technical skills data structure
export const technicalSkills: TechnicalSkill[] = [
  {
    name: "React",
    level: 95,
    category: "Frontend",
    icon: SiReact,
    experience: "3+ years",
    description: "Advanced component architecture, hooks, context, and performance optimization",
    projects: 15,
    color: "#61DAFB"
  },
  {
    name: "TypeScript",
    level: 90,
    category: "Frontend",
    icon: SiTypescript,
    experience: "2+ years",
    description: "Strong typing, advanced types, generics, and type-safe development",
    projects: 12,
    color: "#3178C6"
  },
  {
    name: "Next.js",
    level: 85,
    category: "Frontend",
    icon: SiNextdotjs,
    experience: "2+ years",
    description: "App Router, SSR, SSG, API routes, and performance optimization",
    projects: 8,
    color: "#000000"
  },
  {
    name: "JavaScript",
    level: 95,
    category: "Frontend",
    icon: SiJavascript,
    experience: "4+ years",
    description: "ES6+, async/await, closures, prototypes, and modern JavaScript patterns",
    projects: 20,
    color: "#F7DF1E"
  },
  {
    name: "HTML5",
    level: 98,
    category: "Frontend",
    icon: SiHtml5,
    experience: "5+ years",
    description: "Semantic markup, accessibility, web standards, and modern HTML features",
    projects: 25,
    color: "#E34F26"
  },
  {
    name: "CSS3",
    level: 95,
    category: "Styling",
    icon: SiCss3,
    experience: "5+ years",
    description: "Flexbox, Grid, animations, responsive design, and modern CSS features",
    projects: 25,
    color: "#1572B6"
  },
  {
    name: "Tailwind CSS",
    level: 92,
    category: "Styling",
    icon: SiTailwindcss,
    experience: "2+ years",
    description: "Utility-first CSS, custom configurations, and component design systems",
    projects: 15,
    color: "#06B6D4"
  },
  {
    name: "Sass/SCSS",
    level: 88,
    category: "Styling",
    icon: SiSass,
    experience: "3+ years",
    description: "Variables, mixins, functions, and modular CSS architecture",
    projects: 10,
    color: "#CC6699"
  },
  {
    name: "Styled Components",
    level: 80,
    category: "Styling",
    icon: SiStyledcomponents,
    experience: "1+ years",
    description: "CSS-in-JS, dynamic styling, and component-based styling",
    projects: 6,
    color: "#DB7093"
  },
  {
    name: "Git",
    level: 90,
    category: "Tools",
    icon: SiGit,
    experience: "4+ years",
    description: "Version control, branching strategies, merge conflicts, and collaboration",
    projects: 25,
    color: "#F05032"
  },
  {
    name: "VS Code",
    level: 95,
    category: "Tools",
    icon: Settings,
    experience: "4+ years",
    description: "Extensions, debugging, integrated terminal, and productivity optimization",
    projects: 25,
    color: "#007ACC"
  },
  {
    name: "Figma",
    level: 85,
    category: "Design",
    icon: SiFigma,
    experience: "2+ years",
    description: "UI/UX design, prototyping, design systems, and developer handoff",
    projects: 12,
    color: "#F24E1E"
  },
  {
    name: "Node.js",
    level: 75,
    category: "Backend",
    icon: SiNodedotjs,
    experience: "1+ years",
    description: "Server-side JavaScript, Express.js, API development, and npm packages",
    projects: 5,
    color: "#339933"
  },
  {
    name: "PostgreSQL",
    level: 70,
    category: "Database",
    icon: SiPostgresql,
    experience: "1+ years",
    description: "Relational databases, SQL queries, and database design",
    projects: 4,
    color: "#336791"
  },
  {
    name: "Prisma",
    level: 75,
    category: "Database",
    icon: SiPrisma,
    experience: "1+ years",
    description: "ORM, database migrations, and type-safe database access",
    projects: 4,
    color: "#2D3748"
  },
  {
    name: "Framer Motion",
    level: 88,
    category: "Animation",
    icon: SiFramer,
    experience: "2+ years",
    description: "Complex animations, gestures, layout animations, and micro-interactions",
    projects: 10,
    color: "#0055FF"
  },
  {
    name: "Jest",
    level: 80,
    category: "Testing",
    icon: SiVitest,
    experience: "1+ years",
    description: "Unit testing, mocking, and test-driven development practices",
    projects: 8,
    color: "#C21325"
  },
  {
    name: "React Testing Library",
    level: 75,
    category: "Testing",
    icon: SiReact,
    experience: "1+ years",
    description: "Component testing, user interaction testing, and accessibility testing",
    projects: 6,
    color: "#61DAFB"
  },
  {
    name: "Express.js",
    level: 78,
    category: "Backend",
    icon: SiNodedotjs,
    experience: "1+ years",
    description: "RESTful API development, middleware, authentication, and error handling",
    projects: 6,
    color: "#339933"
  }
]

// Soft skills with detailed descriptions
export const softSkills: SoftSkill[] = [
  {
    name: "Problem Solving",
    icon: Brain,
    description: "Breaking down complex problems into manageable solutions",
    examples: ["Debugging complex issues", "Optimizing performance", "Architecture decisions"],
    strength: 95
  },
  {
    name: "Critical Thinking",
    icon: Lightbulb,
    description: "Analyzing situations objectively and making informed decisions",
    examples: ["Code reviews", "Technical planning", "Solution evaluation"],
    strength: 90
  },
  {
    name: "Communication",
    icon: MessageSquare,
    description: "Clear technical communication with team members and stakeholders",
    examples: ["Documentation", "Code comments", "Team presentations"],
    strength: 88
  },
  {
    name: "Team Collaboration",
    icon: Users2,
    description: "Working effectively in cross-functional teams and pair programming",
    examples: ["Agile workflows", "Code collaboration", "Knowledge sharing"],
    strength: 92
  },
  {
    name: "Time Management",
    icon: Timer,
    description: "Prioritizing tasks and meeting project deadlines consistently",
    examples: ["Sprint planning", "Task prioritization", "Deadline management"],
    strength: 90
  },
  {
    name: "Attention to Detail",
    icon: Eye,
    description: "Ensuring code quality, catching bugs, and maintaining standards",
    examples: ["Code quality", "Testing", "Standards compliance"],
    strength: 95
  },
  {
    name: "Adaptability",
    icon: Repeat,
    description: "Quickly learning new technologies and adapting to changing requirements",
    examples: ["New frameworks", "Changing requirements", "Technology updates"],
    strength: 88
  },
  {
    name: "Learning Agility",
    icon: TrendingUp,
    description: "Continuously improving skills and staying current with technology trends",
    examples: ["Online courses", "Documentation reading", "Experimentation"],
    strength: 95
  }
]

// Currently learning with progress and timeline
export const currentlyLearning: LearningGoal[] = [
  {
    name: "Advanced React Patterns",
    progress: 75,
    icon: SiReact,
    timeline: "3 months",
    description: "Compound components, render props, higher-order components",
    priority: "High",
    resources: ["Epic React", "React docs", "Advanced patterns course"]
  },
  {
    name: "Frontend Testing",
    progress: 60,
    icon: SiVitest,
    timeline: "4 months",
    description: "Unit testing, integration testing, and E2E testing strategies",
    priority: "High",
    resources: ["Testing Library docs", "Vitest documentation", "Testing courses"]
  },
  {
    name: "GraphQL",
    progress: 40,
    icon: SiGraphql,
    timeline: "2 months",
    description: "Query language, Apollo Client, and modern data fetching",
    priority: "Medium",
    resources: ["GraphQL docs", "Apollo tutorials", "Practical GraphQL"]
  },
  {
    name: "Vue.js",
    progress: 30,
    icon: SiVuedotjs,
    timeline: "1 month",
    description: "Alternative frontend framework and ecosystem exploration",
    priority: "Low",
    resources: ["Vue.js docs", "Vue mastery", "Composition API guide"]
  },
  {
    name: "Backend Development",
    progress: 50,
    icon: SiNodedotjs,
    timeline: "6 months",
    description: "Full-stack development with Node.js and database management",
    priority: "Medium",
    resources: ["Node.js docs", "Express guides", "Database design patterns"]
  },
  {
    name: "Web Performance",
    progress: 70,
    icon: Rocket,
    timeline: "2 months",
    description: "Core Web Vitals, optimization techniques, and performance monitoring",
    priority: "High",
    resources: ["Web.dev", "Performance guides", "Lighthouse documentation"]
  }
]

// Skill categories for filtering - reorganized for full-stack focus
export const skillCategories: CategoryFilter[] = [
  { name: "All", icon: Globe, count: technicalSkills.length },
  { name: "Frontend", icon: Monitor, count: technicalSkills.filter(s => s.category === "Frontend" || s.category === "Styling" || s.category === "Animation").length },
  { name: "Backend", icon: Database, count: technicalSkills.filter(s => s.category === "Backend").length },
  { name: "Database & DevOps", icon: Settings, count: technicalSkills.filter(s => s.category === "Database" || s.category === "Tools").length },
  { name: "Testing", icon: CheckCircle, count: technicalSkills.filter(s => s.category === "Testing").length }
]
