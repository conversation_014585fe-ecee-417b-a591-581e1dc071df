import { LucideIcon } from 'lucide-react'

// Base interface for all skills sections
export interface BaseSectionProps {
  className?: string
}

// Technical skill interface
export interface TechnicalSkill {
  name: string
  level: number
  category: string
  icon: any
  experience: string
  description: string
  projects: number
  color: string
}

// Soft skill interface
export interface SoftSkill {
  name: string
  icon: LucideIcon
  description: string
  examples: string[]
  strength: number
}

// Learning goal interface
export interface LearningGoal {
  name: string
  icon: LucideIcon
  description: string
  timeline: string
  priority: string
  progress: number
  resources: string[]
}

// Category filter interface
export interface CategoryFilter {
  name: string
  icon: LucideIcon
  count: number
}

// Animation variants
export const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
}

export const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4
    }
  }
}

export const staggerContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
      delayChildren: 0.1
    }
  }
}
