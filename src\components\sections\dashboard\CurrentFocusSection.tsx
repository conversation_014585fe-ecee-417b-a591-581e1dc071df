import React, { useRef, Suspense } from 'react';
import { motion } from 'framer-motion';
import { Mail, MapPin, ArrowR<PERSON>, Heart } from 'lucide-react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import * as THREE from 'three';

// --- HELPER FUNCTIONS ---
// Utility for combining class names
const cn = (...classes: (string | undefined | null | false)[]) => {
    return classes.filter(Boolean).join(' ');
}

// --- TYPE DEFINITIONS ---
type BaseSectionProps = {
    className?: string;
};

// --- DEPENDENCY NOTE ---
// This component uses @react-three/fiber and @react-three/drei for the interactive globe.
// Please ensure you have these dependencies installed in your project:
// npm install three @react-three/fiber @react-three/drei
// or
// yarn add three @react-three/fiber @react-three/drei

// --- DATA ---
const technologies = [
    { name: "TypeScript", iconColor: "#3178C6" },
    { name: "React.js", iconColor: "#61DAFB" },
    { name: "Next.js", iconColor: "#FFFFFF" },
    { name: "Node.js", iconColor: "#339933" },
    { name: "PostgreSQL", iconColor: "#336791" },
    { name: "Prisma", iconColor: "#2D3748" },
    { name: "TailwindCSS", iconColor: "#06B6D4" },
    { name: "Framer Motion", iconColor: "#0055FF" },
    { name: "Docker", iconColor: "#2496ED" },
    { name: "AWS", iconColor: "#FF9900" },
];

const services = [
    { title: "API Architecture", description: "Designing robust and scalable APIs." },
    { title: "Database Design", description: "Structuring data for performance." },
    { title: "System Monitoring", description: "Implementing real-time analytics." },
];

// --- REUSABLE BENTO CARD COMPONENT ---
const BentoCard = ({ className, children, delay = 0 }: { className?: string; children: React.ReactNode; delay?: number }) => (
    <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6, delay, ease: [0.25, 1, 0.5, 1] }}
        className={cn(
            "relative flex flex-col bg-neutral-900/50 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] rounded-2xl p-6 overflow-hidden",
            className
        )}
    >
        <div className="relative z-10 flex flex-col h-full">
            {children}
        </div>
    </motion.div>
);

// --- 3D INTERACTIVE GLOBE ---
function Earth() {
    const earthRef = useRef<THREE.Mesh>(null!);
    useFrame(() => {
        if (earthRef.current) {
            earthRef.current.rotation.y += 0.0007; // Slightly faster rotation
        }
    });

    return (
        <mesh ref={earthRef} scale={2.5}>
            <sphereGeometry args={[1, 64, 64]} />
            {/* Using colors from the new design system */}
            <meshStandardMaterial
                color="hsl(var(--primary) / 0.5)"
                metalness={0.2}
                roughness={0.9}
                emissive="hsl(var(--primary) / 0.1)"
                emissiveIntensity={0.5}
            />
            <mesh scale={1.01}>
                <sphereGeometry args={[1, 32, 32]} />
                <meshBasicMaterial
                    color="hsl(var(--accent))"
                    wireframe
                    transparent
                    opacity={0.15}
                />
            </mesh>
        </mesh>
    );
}

const InteractiveGlobe = () => (
    <div className="relative w-full h-full min-h-[250px] cursor-grab active:cursor-grabbing">
        <Canvas camera={{ position: [0, 0, 4], fov: 45 }}>
            <Suspense fallback={null}>
                <ambientLight intensity={0.3} />
                <directionalLight position={[5, 5, 5]} intensity={2} color="hsl(var(--foreground))" />
                <Earth />
                <OrbitControls enableZoom={false} enablePan={false} autoRotate={false} />
            </Suspense>
        </Canvas>
    </div>
);


// --- BENTO GRID CARD COMPONENTS ---

// The definitive version of the CollaborationCard, polished to perfection.
const CollaborationCard = () => {
    // A completely redesigned Ring component for a soft, atmospheric effect.
    const Ring = () => (
        <div className="h-56 w-56 flex-shrink-0 rounded-full bg-black/20" />
    );

    return (
        <BentoCard className="md:col-span-2 overflow-hidden p-0">
            
            {/* Background: Using soft, overlapping circles for a subtle, integrated feel. */}
            <div className="absolute inset-0 flex items-center justify-center">
                <div className="flex w-full items-center justify-center -space-x-20">
                    <Ring />
                    <Ring />
                    <Ring />
                    <Ring />
                    <Ring />
                </div>
            </div>

            {/* Profile Image: Centered with the sharp yellow accent ring. */}
            <div className="absolute left-1/2 top-1/2 z-10 -translate-x-1/2 -translate-y-1/2">
                 <div className="relative h-24 w-24">
                     {/* The sharp yellow accent ring */}
                     <div className="absolute -inset-1.5 rounded-full bg-yellow-400" />
                     <img
                        src="/image/profile.png" // Ensure this path is correct
                        alt="CJ Jutba"
                        className="relative h-full w-full rounded-full border-2 border-neutral-800 object-cover"
                    />
                </div>
            </div>

            {/* Text Content: Final positioning and styling for a softer aesthetic. */}
            <div className="absolute bottom-6 left-6 z-10">
                <div className="mb-3">
                    {/* New icon: Interlocking circles (infinity symbol) to match the reference. */}
                    <svg width="28" height="28" viewBox="0 0 24 12" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-6 w-12 text-neutral-400">
                        <path d="M9.52 1.53C7.39 3.12 7.29 6.42 9.32 8.32C11.35 10.22 14.65 10.52 16.78 9.02C18.91 7.52 19.21 4.22 17.18 2.32C15.15 0.420001 11.85 -0.0799994 9.52 1.53Z" stroke="currentColor" strokeWidth="1.5"/>
                        <path d="M14.48 10.47C12.35 8.88 12.25 5.58 14.28 3.68C16.31 1.78 19.61 1.48 21.74 2.98C23.87 4.48 24.17 7.78 22.14 9.68C20.11 11.58 16.81 12.06 14.48 10.47Z" stroke="currentColor" strokeWidth="1.5"/>
                    </svg>
                </div>
                <h3 className="mb-2 text-xs font-medium uppercase tracking-wider text-neutral-500">
                    Collaboration
                </h3>
                {/* Final text content with precise line breaks. */}
                <p className="max-w-[240px] text-base font-light text-neutral-400">
                    I prioritize client collaboration, <br /> fostering <br /> open communication
                </p>
            </div>
        </BentoCard>
    );
};

// Left Card (Spans 2 rows)
const TimezoneCard = () => (
    <BentoCard className="md:row-span-2">
        <div className="w-full">
            <h3 className="text-2xl font-semibold text-foreground">Flexible with Timezones</h3>
            <p className="text-primary mb-4">Global Communication</p>
            <div className="flex gap-2 mb-4">
                {["🇬🇧 UK", "🇮🇳 India", "🇺🇸 USA"].map((zone) => (
                    <div key={zone} className="px-3 py-1 bg-secondary border border-border rounded-md text-xs text-secondary-foreground">
                        {zone}
                    </div>
                ))}
            </div>
        </div>
        <div className="flex-grow flex items-center justify-center -mx-6">
            <InteractiveGlobe />
        </div>
        <div className="flex items-center gap-2 text-sm text-foreground mt-auto pt-4">
            <MapPin className="w-4 h-4 text-primary" />
            <span>Remote</span>
            <span className="text-muted-foreground">/</span>
            <span className="text-primary">Philippines</span>
        </div>
    </BentoCard>
);

// Center Card
const CtaCard = () => (
    <BentoCard className="justify-center items-center text-center">
        <img 
            src="/image/cj-transparent-white.png" 
            alt="CJ Logo"
            className="w-24 h-24 mb-4"
        />
        <h3 className="text-2xl font-semibold text-foreground">Let's work together</h3>
        <p className="text-muted-foreground mb-6">on your next project.</p>
        <a href="mailto:<EMAIL>" className="w-full flex items-center justify-center gap-2 px-4 py-2.5 bg-primary hover:bg-primary/90 rounded-lg text-primary-foreground text-sm font-semibold transition-colors mt-auto">
            <Mail className="w-4 h-4" />
            <EMAIL>
        </a>
    </BentoCard>
);

// Right Card (Spans 2 rows)
const TechStackCard = () => (
    <BentoCard className="md:row-span-2">
        <h3 className="text-2xl font-semibold text-foreground mb-4">
            Core Technologies
        </h3>
        <div className="grid grid-cols-2 gap-3 flex-grow">
            {technologies.map((tech, index) => (
                <motion.div
                    key={tech.name}
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="flex items-center gap-3 px-3 py-2 bg-secondary border border-border rounded-lg text-sm text-secondary-foreground hover:bg-border transition-colors"
                >
                    <div className="w-2 h-2 rounded-full" style={{ backgroundColor: tech.iconColor, boxShadow: `0 0 8px 0 ${tech.iconColor}` }} />
                    {tech.name}
                </motion.div>
            ))}
        </div>
    </BentoCard>
);

// Bottom Card (Spans 2 columns)
const ServicesCard = () => (
    <BentoCard className="md:col-span-2">
        <h3 className="text-muted-foreground">The Inside Scoop</h3>
        <h2 className="text-2xl font-semibold text-foreground mb-4">Building a SaaS Application</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm flex-grow">
            {services.map(service => (
                <div key={service.title} className="bg-secondary p-4 rounded-lg border border-border">
                    <h4 className="font-semibold text-foreground mb-1">{service.title}</h4>
                    <p className="text-muted-foreground text-xs">{service.description}</p>
                </div>
            ))}
        </div>
    </BentoCard>
);

// --- MAIN SECTION COMPONENT ---
export default function CurrentFocusSection({ className }: BaseSectionProps) {
    return (
        <section className={cn('py-20 lg:py-32 bg-background text-foreground', className)} id="current-focus">
            <div className="container mx-auto px-4">
                <div 
                    className="grid grid-cols-1 md:grid-cols-3 md:grid-rows-3 gap-6 max-w-7xl mx-auto"
                    style={{ minHeight: '600px', height: '90vh' }}
                >
                    <CollaborationCard />
                    <TechStackCard />
                    <TimezoneCard />
                    <CtaCard />
                    <ServicesCard />
                </div>
            </div>
        </section>
    );
}
