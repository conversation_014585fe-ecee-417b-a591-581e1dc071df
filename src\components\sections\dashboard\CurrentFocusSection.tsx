import React, { useRef, Suspense } from 'react';
import { motion, useMotionValue, useSpring } from 'framer-motion';
import { Mail, MapPin, ArrowRight, Globe, Sparkles, Code2, Database, Monitor, Zap, Heart } from 'lucide-react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import * as THREE from 'three';
import { cn } from '@/lib/utils';

// --- TYPE DEFINITIONS ---
type BaseSectionProps = {
    className?: string;
};

// --- ANIMATION VARIANTS ---
const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
            delayChildren: 0.2
        }
    }
};

const cardVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
        opacity: 1,
        y: 0,
        scale: 1,
        transition: {
            duration: 0.6,
            ease: [0.25, 1, 0.5, 1]
        }
    }
};

const techItemVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
        opacity: 1,
        scale: 1,
        transition: {
            duration: 0.4,
            ease: "backOut"
        }
    }
};

// --- DATA ---
const technologies = [
    { name: "TypeScript", iconColor: "#3178C6", category: "Language" },
    { name: "React.js", iconColor: "#61DAFB", category: "Frontend" },
    { name: "Next.js", iconColor: "#FFFFFF", category: "Framework" },
    { name: "Node.js", iconColor: "#339933", category: "Backend" },
    { name: "PostgreSQL", iconColor: "#336791", category: "Database" },
    { name: "Prisma", iconColor: "#2D3748", category: "ORM" },
    { name: "TailwindCSS", iconColor: "#06B6D4", category: "Styling" },
    { name: "Framer Motion", iconColor: "#0055FF", category: "Animation" },
    { name: "Docker", iconColor: "#2496ED", category: "DevOps" },
    { name: "AWS", iconColor: "#FF9900", category: "Cloud" },
];

const services = [
    {
        title: "API Architecture",
        description: "Designing robust and scalable REST & GraphQL APIs with proper authentication and rate limiting.",
        icon: Code2,
        gradient: "from-blue-500/20 to-cyan-500/20"
    },
    {
        title: "Database Design",
        description: "Structuring relational and NoSQL databases for optimal performance and scalability.",
        icon: Database,
        gradient: "from-green-500/20 to-emerald-500/20"
    },
    {
        title: "System Monitoring",
        description: "Implementing comprehensive logging, metrics, and real-time analytics dashboards.",
        icon: Monitor,
        gradient: "from-purple-500/20 to-pink-500/20"
    },
];

// --- PREMIUM BENTO CARD COMPONENT ---
const BentoCard = ({
    className,
    children,
    delay = 0,
    gradient = false,
    interactive = true
}: {
    className?: string;
    children: React.ReactNode;
    delay?: number;
    gradient?: boolean;
    interactive?: boolean;
}) => {
    const mouseX = useMotionValue(0);
    const mouseY = useMotionValue(0);
    const rotateX = useSpring(mouseY, { stiffness: 300, damping: 30 });
    const rotateY = useSpring(mouseX, { stiffness: 300, damping: 30 });

    const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
        if (!interactive) return;

        const rect = e.currentTarget.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        const rotateXValue = (e.clientY - centerY) / 10;
        const rotateYValue = (centerX - e.clientX) / 10;

        mouseX.set(rotateYValue);
        mouseY.set(rotateXValue);
    };

    const handleMouseLeave = () => {
        if (!interactive) return;
        mouseX.set(0);
        mouseY.set(0);
    };

    return (
        <motion.div
            variants={cardVariants}
            onMouseMove={handleMouseMove}
            onMouseLeave={handleMouseLeave}
            style={interactive ? { rotateX, rotateY, transformStyle: "preserve-3d" } : {}}
            className={cn(
                "group relative flex flex-col bg-card/80 backdrop-blur-xl border border-border/50 rounded-2xl p-6 overflow-hidden transition-all duration-500",
                "hover:bg-card/90 hover:border-border hover:shadow-2xl hover:shadow-primary/5",
                gradient && "bg-gradient-to-br from-card/90 to-card/60",
                interactive && "cursor-pointer transform-gpu",
                className
            )}
            whileHover={interactive ? { scale: 1.02, y: -4 } : {}}
            transition={{ duration: 0.3, ease: "easeOut" }}
        >
            {/* Subtle gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

            {/* Content */}
            <div className="relative z-10 flex flex-col h-full">
                {children}
            </div>

            {/* Animated border glow */}
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-primary/20 via-accent/20 to-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10 blur-xl" />
        </motion.div>
    );
};

// --- ENHANCED 3D INTERACTIVE GLOBE ---
function Earth() {
    const earthRef = useRef<THREE.Mesh>(null!);
    const wireframeRef = useRef<THREE.Mesh>(null!);

    useFrame((state) => {
        if (earthRef.current) {
            earthRef.current.rotation.y += 0.005;
        }
        if (wireframeRef.current) {
            wireframeRef.current.rotation.y += 0.003;
            wireframeRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
        }
    });

    return (
        <group>
            {/* Main sphere */}
            <mesh ref={earthRef} scale={1.8}>
                <sphereGeometry args={[1, 32, 32]} />
                <meshStandardMaterial
                    color="#9438D5"
                    metalness={0.3}
                    roughness={0.7}
                    emissive="#9438D5"
                    emissiveIntensity={0.1}
                />
            </mesh>

            {/* Wireframe overlay */}
            <mesh ref={wireframeRef} scale={1.85}>
                <sphereGeometry args={[1, 16, 16]} />
                <meshBasicMaterial
                    color="#DE55C8"
                    wireframe
                    transparent
                    opacity={0.3}
                />
            </mesh>

            {/* Outer glow */}
            <mesh scale={2.1}>
                <sphereGeometry args={[1, 16, 16]} />
                <meshBasicMaterial
                    color="#9438D5"
                    transparent
                    opacity={0.05}
                />
            </mesh>
        </group>
    );
}

const InteractiveGlobe = () => (
    <div className="relative w-full h-full min-h-[200px] flex items-center justify-center">
        <div className="w-48 h-48 relative">
            <Canvas
                camera={{ position: [0, 0, 4], fov: 50 }}
                gl={{ antialias: true, alpha: true }}
                dpr={[1, 2]}
            >
                <Suspense fallback={null}>
                    <ambientLight intensity={0.4} />
                    <directionalLight
                        position={[5, 5, 5]}
                        intensity={1.5}
                        color="#ffffff"
                    />
                    <pointLight
                        position={[-5, -5, -5]}
                        intensity={0.5}
                        color="#DE55C8"
                    />
                    <Earth />
                    <OrbitControls
                        enableZoom={false}
                        enablePan={false}
                        autoRotate={false}
                        enableDamping
                        dampingFactor={0.05}
                    />
                </Suspense>
            </Canvas>
        </div>
    </div>
);


// --- BENTO GRID CARD COMPONENTS ---

const CollaborationCard = () => {
    return (
        <BentoCard className="md:col-span-2 md:row-span-1" gradient>
            <div className="flex items-center justify-between h-full">
                {/* Left content */}
                <div className="flex-1">
                    <div className="flex items-center gap-3 mb-4">
                        <div className="p-2.5 bg-gradient-to-br from-primary/20 to-accent/20 rounded-xl backdrop-blur-sm border border-primary/20">
                            <Heart className="w-5 h-5 text-primary" />
                        </div>
                        <div>
                            <h3 className="text-heading text-lg font-semibold text-foreground">Collaboration</h3>
                            <p className="text-sm text-muted-foreground">Building together</p>
                        </div>
                    </div>

                    <p className="text-muted-foreground text-sm leading-relaxed font-geometric max-w-xs">
                        I prioritize client collaboration, fostering open communication
                        throughout the development process.
                    </p>
                </div>

                {/* Right content - Profile with animated rings */}
                <div className="relative flex items-center justify-center w-32 h-32">
                    {/* Animated background rings */}
                    <motion.div
                        className="absolute w-24 h-24 rounded-full border border-primary/30"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    />
                    <motion.div
                        className="absolute w-28 h-28 rounded-full border border-accent/20"
                        animate={{ rotate: -360 }}
                        transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
                    />

                    {/* Profile image with premium styling */}
                    <div className="relative z-10 w-16 h-16 rounded-full bg-gradient-to-br from-primary to-accent p-0.5">
                        <div className="w-full h-full rounded-full bg-card flex items-center justify-center overflow-hidden">
                            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center">
                                <Globe className="w-6 h-6 text-primary" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </BentoCard>
    );
};

const TimezoneCard = () => (
    <BentoCard className="md:row-span-2" gradient>
        <div className="flex flex-col h-full">
            {/* Header */}
            <div className="flex items-center gap-3 mb-6">
                <div className="p-2.5 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-xl backdrop-blur-sm border border-purple-500/20">
                    <Globe className="w-5 h-5 text-purple-400" />
                </div>
                <div>
                    <h3 className="text-heading text-lg font-semibold text-foreground">Flexible with Timezones</h3>
                    <p className="text-sm text-muted-foreground">Global Communication</p>
                </div>
            </div>

            {/* Timezone indicators */}
            <div className="flex justify-between items-center mb-6">
                <div className="flex flex-col items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-green-400 animate-pulse" />
                    <span className="text-xs text-muted-foreground font-geometric">🇬🇧 UK</span>
                </div>
                <div className="flex flex-col items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-yellow-400 animate-pulse" />
                    <span className="text-xs text-muted-foreground font-geometric">🇮🇳 India</span>
                </div>
                <div className="flex flex-col items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-blue-400 animate-pulse" />
                    <span className="text-xs text-muted-foreground font-geometric">🇺🇸 USA</span>
                </div>
            </div>

            {/* Globe visualization */}
            <div className="flex-1 flex items-center justify-center">
                <InteractiveGlobe />
            </div>

            {/* Footer */}
            <div className="mt-auto flex items-center gap-2 text-xs text-muted-foreground">
                <MapPin className="w-3 h-3" />
                <span className="font-geometric">Remote / Philippines</span>
            </div>
        </div>
    </BentoCard>
);

const CtaCard = () => (
    <BentoCard className="md:col-span-1 md:row-span-1 flex flex-col justify-center items-center text-center" gradient>
        <div className="relative mb-6">
            {/* Animated glow effect */}
            <motion.div
                className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full blur-xl"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            />
            <img
                src="/image/cj-transparent-white.png"
                alt="CJ Logo"
                className="relative w-16 h-16 object-contain"
            />
        </div>

        <h3 className="text-heading text-xl font-semibold text-foreground mb-2">Let's work together</h3>
        <p className="text-muted-foreground text-sm mb-6 font-geometric">on your next project.</p>

        <motion.a
            href="mailto:<EMAIL>"
            className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 rounded-xl text-primary-foreground text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
        >
            <Mail className="w-4 h-4" />
            <span className="font-geometric"><EMAIL></span>
        </motion.a>
    </BentoCard>
);

const TechStackCard = () => (
    <BentoCard className="md:row-span-2" gradient>
        <div className="flex flex-col h-full">
            {/* Header */}
            <div className="flex items-center gap-3 mb-6">
                <div className="p-2.5 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-xl backdrop-blur-sm border border-blue-500/20">
                    <Code2 className="w-5 h-5 text-blue-400" />
                </div>
                <div>
                    <h3 className="text-heading text-lg font-semibold text-foreground">Core Technologies</h3>
                    <p className="text-sm text-muted-foreground">Modern stack</p>
                </div>
            </div>

            {/* Technology grid */}
            <motion.div
                className="grid grid-cols-2 gap-2 mb-6 flex-grow"
                variants={containerVariants}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
            >
                {technologies.map((tech, index) => (
                    <motion.div
                        key={tech.name}
                        variants={techItemVariants}
                        className="flex items-center gap-2 px-3 py-2 bg-secondary/50 border border-border/50 rounded-lg text-xs text-secondary-foreground hover:bg-secondary hover:border-border transition-all duration-300"
                    >
                        <div
                            className="w-2 h-2 rounded-full bg-primary shadow-sm"
                            data-tech-color={tech.iconColor}
                        />
                        <span className="font-geometric font-medium">{tech.name}</span>
                    </motion.div>
                ))}
            </motion.div>

            {/* Stats section */}
            <div className="mt-auto space-y-3">
                <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">Experience</span>
                    <span className="text-foreground font-semibold">5+ Years</span>
                </div>
                <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">Projects</span>
                    <span className="text-foreground font-semibold">50+ Completed</span>
                </div>
                <div className="w-full bg-secondary/50 rounded-full h-1.5">
                    <motion.div
                        className="bg-gradient-to-r from-primary to-accent h-1.5 rounded-full"
                        initial={{ width: 0 }}
                        whileInView={{ width: "95%" }}
                        transition={{ duration: 1.5, delay: 0.5 }}
                    />
                </div>
            </div>
        </div>
    </BentoCard>
);

const ServicesCard = () => (
    <BentoCard className="md:col-span-2 md:row-span-1" gradient>
        <div className="flex flex-col h-full">
            {/* Header */}
            <div className="mb-6">
                <div className="flex items-center gap-2 mb-2">
                    <Sparkles className="w-4 h-4 text-accent" />
                    <span className="text-xs text-muted-foreground font-geometric uppercase tracking-wider">The Inside Scoop</span>
                </div>
                <h2 className="text-heading text-xl font-semibold text-foreground">Building a SaaS Application</h2>
            </div>

            {/* Services grid */}
            <motion.div
                className="grid grid-cols-1 md:grid-cols-3 gap-4 flex-grow"
                variants={containerVariants}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
            >
                {services.map((service, index) => {
                    const IconComponent = service.icon;
                    return (
                        <motion.div
                            key={service.title}
                            variants={cardVariants}
                            className={cn(
                                "relative p-4 rounded-xl border border-border/50 backdrop-blur-sm transition-all duration-300 hover:border-border group",
                                "bg-gradient-to-br", service.gradient
                            )}
                            whileHover={{ y: -2 }}
                        >
                            <div className="flex items-center gap-3 mb-3">
                                <div className="p-2 bg-background/50 rounded-lg">
                                    <IconComponent className="w-4 h-4 text-foreground" />
                                </div>
                                <h4 className="font-semibold text-foreground text-sm font-geometric">{service.title}</h4>
                            </div>
                            <p className="text-muted-foreground text-xs leading-relaxed font-geometric">{service.description}</p>

                            {/* Subtle hover glow */}
                            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        </motion.div>
                    );
                })}
            </motion.div>
        </div>
    </BentoCard>
);

// --- MAIN SECTION COMPONENT ---
export default function CurrentFocusSection({ className }: BaseSectionProps) {
    return (
        <section className={cn('py-20 lg:py-32 bg-background text-foreground', className)} id="current-focus">
            <div className="container mx-auto px-4">
                {/* Section Header */}
                <motion.div
                    className="text-center mb-16"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6 }}
                >
                    <h2 className="text-display text-3xl md:text-4xl font-bold text-foreground mb-4">
                        Current Focus
                    </h2>
                    <p className="text-muted-foreground text-lg font-geometric max-w-2xl mx-auto">
                        Exploring the intersection of design and development through modern technologies and collaborative approaches.
                    </p>
                </motion.div>

                {/* Bento Grid */}
                <motion.div
                    className="grid grid-cols-1 md:grid-cols-3 md:grid-rows-3 gap-6 max-w-7xl mx-auto min-h-[600px] md:h-[90vh]"
                    variants={containerVariants}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, margin: "-100px" }}
                >
                    <CollaborationCard />
                    <TechStackCard />
                    <TimezoneCard />
                    <CtaCard />
                    <ServicesCard />
                </motion.div>
            </div>
        </section>
    );
}
