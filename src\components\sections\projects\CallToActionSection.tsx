import React from "react"
import { motion } from "framer-motion"
import { <PERSON> } from "lucide-react"
import { PrimaryButton, OutlineButton } from "@/components/ui/enhanced-button"
import { Card, CardContent } from "@/components/ui/card"
import { BaseSectionProps, containerVariants, itemVariants } from "./types"
import { createNavigationHandler } from "./utils"

export function CallToActionSection({ className }: BaseSectionProps) {
  const handleNavigation = createNavigationHandler()

  return (
    <motion.div
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      variants={containerVariants}
      className={`text-center ${className || ""}`}
    >
      <Card className="border-border/50 bg-gradient-to-br from-card/90 to-card/50 backdrop-blur-sm">
        <CardContent className="p-8 md:p-12">
          <motion.div variants={itemVariants} className="space-y-6">
            <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-primary/10 dark:from-primary/30 dark:to-primary/20 rounded-2xl flex items-center justify-center mx-auto">
              <Rocket className="w-8 h-8 text-primary" />
            </div>

            <div className="space-y-4">
              <h3 className="text-2xl md:text-3xl font-bold text-foreground">
                Ready to Start Your Next Project?
              </h3>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                Let's collaborate to bring your ideas to life. Whether it's a web application, 
                mobile app, or custom solution, I'm here to help you build something amazing.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-4">
              <PrimaryButton
                onClick={() => handleNavigation('/contact')}
                className="px-8 py-4"
                arrowAnimation="slide"
              >
                Start a Project
              </PrimaryButton>
              <OutlineButton
                onClick={() => handleNavigation('/about')}
                className="px-8 py-4"
                arrowAnimation="fade"
              >
                Learn More About Me
              </OutlineButton>
            </div>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
