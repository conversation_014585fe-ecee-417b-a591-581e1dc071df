import { Project } from "./types"

export const projects: Project[] = [
  {
    id: 1,
    title: "E-commerce Platform",
    description: "A complete retail solution built from the ground up, demonstrating end-to-end development from PostgreSQL database design to React frontend implementation.",
    longDescription: "To handle complex product and order management, I engineered a robust RESTful API using Node.js and Express.js, with <PERSON>risma managing all PostgreSQL database interactions. This powerful backend provides real-time data to a fast, responsive React frontend built with TypeScript for type safety. The result is a seamless shopping experience where users can browse products, manage their cart, and complete purchases with confidence. Advanced features include real-time inventory management, personalized recommendations, and a responsive design that works flawlessly across all devices.",
    image: "/api/placeholder/600/400",
    technologies: ["React", "Next.js", "TypeScript", "Tailwind CSS", "Node.js", "PostgreSQL", "Prisma", "Stripe"],
    category: "Full-Stack",
    type: "Web Application",
    status: "Completed",
    featured: true,
    liveUrl: "https://shophub-demo.vercel.app",
    githubUrl: "https://github.com/cjjutba/ecommerce-platform",
    highlight: "Full-stack development with modern architecture",
    features: [
      "User authentication & authorization",
      "Shopping cart & checkout flow",
      "Payment processing with Stripe",
      "Admin dashboard for inventory management",
      "Responsive design for all devices",
      "Real-time order tracking"
    ],
    challenges: "Implementing secure payment processing and managing complex state across multiple user flows.",
    learnings: "Gained deep understanding of e-commerce architecture, payment systems, and user experience optimization.",
    duration: "3 months",
    teamSize: "Solo project",
    year: "2024",
    deviceType: "desktop" as const
  },
  {
    id: 2,
    title: "Task Management App",
    description: "A comprehensive productivity platform built with full-stack architecture, featuring real-time collaboration through WebSocket connections and complex database relationships.",
    longDescription: "I built a comprehensive task management system with a PostgreSQL database designed for complex relationships between users, projects, and tasks. The Express.js API handles real-time updates through WebSocket connections, while the React frontend provides an intuitive drag-and-drop interface. Key features include role-based permissions, real-time collaboration, file attachments, and detailed analytics. The application scales seamlessly from individual use to enterprise teams, with robust data synchronization ensuring consistency across all connected clients.",
    image: "/api/placeholder/600/400",
    technologies: ["Next.js", "React", "TypeScript", "Prisma", "PostgreSQL", "Tailwind CSS", "Socket.io"],
    category: "Full-Stack",
    type: "Web Application",
    status: "In Progress",
    featured: true,
    liveUrl: "https://taskflow-demo.vercel.app",
    githubUrl: "https://github.com/cjjutba/task-management",
    highlight: "Real-time collaboration with advanced project management",
    features: [
      "Real-time collaboration with Socket.io",
      "Drag-and-drop task management",
      "Team member management",
      "Project timeline visualization",
      "File sharing and comments",
      "Advanced filtering and search"
    ],
    challenges: "Implementing real-time synchronization across multiple users while maintaining data consistency.",
    learnings: "Mastered WebSocket implementation, database optimization, and complex state management patterns.",
    duration: "4 months",
    teamSize: "Solo project",
    year: "2024",
    deviceType: "mobile" as const
  },
  {
    id: 3,
    title: "Portfolio Website",
    description: "A performance-optimized showcase of full-stack development skills, demonstrating modern React architecture, advanced animations, and accessibility best practices.",
    longDescription: "This portfolio represents the culmination of my development skills, featuring a carefully architected React application with TypeScript for type safety and Tailwind CSS for consistent styling. I implemented advanced animations with Framer Motion, created an interactive terminal component, and optimized performance for lightning-fast load times. The site demonstrates modern development practices including component-based architecture, responsive design, accessibility compliance, and SEO optimization. Every interaction is carefully crafted to provide a smooth, engaging user experience while showcasing the depth of my technical capabilities.",
    image: "/api/placeholder/600/400",
    technologies: ["React", "TypeScript", "Tailwind CSS", "Vite", "Framer Motion"],
    category: "Frontend",
    type: "Portfolio",
    status: "Completed",
    featured: true,
    liveUrl: "https://chris-portfolio.vercel.app",
    githubUrl: "https://github.com/cjjutba/portfolio-v2",
    highlight: "Modern design with smooth animations and interactions",
    features: [
      "Responsive design system",
      "Smooth page transitions",
      "Interactive project showcases",
      "Dark/light theme support",
      "Performance optimized",
      "SEO friendly"
    ],
    challenges: "Creating smooth animations while maintaining performance across different devices and browsers.",
    learnings: "Advanced CSS techniques, animation principles, and performance optimization strategies.",
    duration: "2 months",
    teamSize: "Solo project",
    year: "2024",
    deviceType: "desktop" as const
  },
  {
    id: 4,
    title: "Weather Dashboard",
    description: "A comprehensive weather application with location-based forecasts, interactive maps, and detailed weather analytics.",
    longDescription: "WeatherPro provides accurate weather information with a beautiful, intuitive interface. Features include 7-day forecasts, weather maps, severe weather alerts, and historical weather data visualization.",
    image: "/api/placeholder/600/400",
    technologies: ["React", "JavaScript", "Tailwind CSS", "Chart.js", "OpenWeather API"],
    category: "Frontend",
    type: "Web Application",
    status: "Completed",
    featured: false,
    liveUrl: "https://weather-dashboard-demo.vercel.app",
    githubUrl: "https://github.com/cjjutba/weather-dashboard",
    highlight: "API integration with beautiful data visualization",
    features: [
      "Location-based weather data",
      "7-day weather forecast",
      "Interactive weather maps",
      "Weather alerts and notifications",
      "Historical weather charts",
      "Responsive mobile design"
    ],
    challenges: "Handling API rate limits and creating smooth data visualizations with real-time updates.",
    learnings: "API integration patterns, data visualization techniques, and geolocation services.",
    duration: "1 month",
    teamSize: "Solo project",
    year: "2023",
    deviceType: "mobile" as const
  },
  {
    id: 5,
    title: "Recipe Finder App",
    description: "A mobile-first recipe discovery application with ingredient-based search, meal planning, and shopping list features.",
    longDescription: "ChefMate helps users discover new recipes based on available ingredients, dietary preferences, and cooking time. The app includes meal planning tools, nutritional information, and integrated shopping lists.",
    image: "/api/placeholder/600/400",
    technologies: ["React", "TypeScript", "Tailwind CSS", "Spoonacular API", "Local Storage"],
    category: "Frontend",
    type: "Mobile App",
    status: "Completed",
    featured: false,
    liveUrl: "https://recipe-finder-demo.vercel.app",
    githubUrl: "https://github.com/cjjutba/recipe-finder",
    highlight: "Mobile-first design with advanced filtering capabilities",
    features: [
      "Ingredient-based recipe search",
      "Dietary restriction filters",
      "Meal planning calendar",
      "Shopping list generation",
      "Nutritional information display",
      "Favorite recipes collection"
    ],
    challenges: "Optimizing search performance and creating an intuitive mobile interface for complex filtering.",
    learnings: "Mobile UX design principles, advanced search algorithms, and local data management.",
    duration: "6 weeks",
    teamSize: "Solo project",
    year: "2023",
    deviceType: "mobile" as const
  },
  {
    id: 6,
    title: "Expense Tracker",
    description: "A personal finance management tool with budget tracking, expense categorization, and financial insights.",
    longDescription: "MoneyWise provides comprehensive expense tracking with intelligent categorization, budget management, and detailed financial analytics. Built with a focus on privacy and data security.",
    image: "/api/placeholder/600/400",
    technologies: ["Next.js", "React", "TypeScript", "Prisma", "PostgreSQL", "Chart.js"],
    category: "Full-Stack",
    type: "Web Application",
    status: "In Progress",
    featured: false,
    liveUrl: "#",
    githubUrl: "https://github.com/cjjutba/expense-tracker",
    highlight: "Financial data visualization with secure user authentication",
    features: [
      "Expense categorization and tracking",
      "Budget creation and monitoring",
      "Financial insights and analytics",
      "Receipt photo upload",
      "Monthly/yearly reports",
      "Data export functionality"
    ],
    challenges: "Implementing secure financial data handling and creating meaningful data visualizations.",
    learnings: "Financial application architecture, data security best practices, and advanced charting libraries.",
    duration: "2 months",
    teamSize: "Solo project",
    year: "2024",
    deviceType: "desktop" as const
  }
]
