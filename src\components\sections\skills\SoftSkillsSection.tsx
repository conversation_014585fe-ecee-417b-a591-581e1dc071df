import React from 'react'
import { motion } from 'framer-motion'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { cn } from '@/lib/utils'
import { BaseSectionProps, staggerContainerVariants, itemVariants } from './types'
import { professionalSkills as softSkills } from '@/data/skills/skillsData'

export default function SoftSkillsSection({ className }: BaseSectionProps) {
  const getStrengthColor = (strength: number) => {
    if (strength >= 90) return "text-green-600 dark:text-green-400"
    if (strength >= 80) return "text-blue-600 dark:text-blue-400"
    if (strength >= 70) return "text-yellow-600 dark:text-yellow-400"
    return "text-orange-600 dark:text-orange-400"
  }

  return (
    <section className={cn('py-24 sm:py-32', className)}>
      {/* Section Header */}
      <div className="mb-20 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-2"
        >
          <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
            THE INTANGIBLES THAT DRIVE SUCCESS
          </span>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight"
        >
          Professional{' '}
          <span className="relative font-apparel font-black italic -skew-x-12 inline-block bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift [filter:drop-shadow(0_0_15px_rgba(148,56,213,0.5))_drop-shadow(0_0_1.5px_rgba(0,0,0,0.2))]">
            Competencies
          </span>
        </motion.h2>
      </div>

      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={staggerContainerVariants}
        className="max-w-6xl mx-auto"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {softSkills.map((skill) => (
            <motion.div key={skill.name} variants={itemVariants}>
              <Card className={cn(
                'group h-full transition-all duration-300 transform hover:scale-[1.02]',
                'bg-neutral-900/50 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] rounded-2xl border-transparent'
              )}>
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-10 h-10 rounded-lg bg-neutral-800/60 flex items-center justify-center">
                      <skill.icon className="w-5 h-5 text-primary" />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-lg font-bold group-hover:text-primary transition-colors duration-300">
                        {skill.name}
                      </CardTitle>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <Progress value={skill.level} className="flex-1 h-2" />
                    <Badge
                      variant="secondary"
                      className={`ml-3 font-bold ${getStrengthColor(skill.level)}`}
                    >
                      {skill.level}%
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm leading-relaxed mb-4">
                    {skill.description}
                  </CardDescription>
                  <div className="space-y-2">
                    <p className="text-xs font-medium text-primary">Key Applications:</p>
                    <div className="flex flex-wrap gap-1">
                      {skill.examples.map((example, idx) => (
                        <Badge
                          key={idx}
                          variant="outline"
                          className="text-xs px-2 py-1"
                        >
                          {example}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </section>
  )
}
