import React, { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { ExternalLink, Github, Users, Zap } from "lucide-react"
import { CardButton } from "@/components/ui/enhanced-button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Section from "@/components/Section"
import { BaseSectionProps, containerVariants, itemVariants } from "./types"
import { renderIcon, techIcons, getStatusColor, getFilterCategories, getProjectsByCategory, getDeviceIcon } from "./utils"
import { projects } from "@/data/projects/projectsData"
import { cn } from "@/lib/utils"

export function ProjectsGridSection({ className }: BaseSectionProps) {
  const [activeFilter, setActiveFilter] = useState("All")
  const filterCategories = getFilterCategories(projects)
  const filteredProjects = getProjectsByCategory(projects, activeFilter)

  return (
    <Section
      title="All Projects"
      subtitle="Explore my complete portfolio organized by category and technology stack."
      className={className}
    >
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={containerVariants}
        className="space-y-8"
      >
        {/* Filter Tabs */}
        <motion.div
          variants={itemVariants}
          className="flex flex-wrap justify-center gap-2 sm:gap-4"
        >
          {filterCategories.map((category) => (
            <motion.button
              key={category.name}
              onClick={() => setActiveFilter(category.name)}
              className={cn(
                "px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 flex items-center gap-2",
                activeFilter === category.name
                  ? "bg-primary text-primary-foreground shadow-lg scale-105"
                  : "bg-card/50 text-muted-foreground hover:bg-card hover:text-foreground border border-border/50"
              )}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {renderIcon(category.icon, "w-4 h-4")}
              {category.name}
              <Badge
                variant={activeFilter === category.name ? "secondary" : "outline"}
                className="ml-1 text-xs"
              >
                {category.count}
              </Badge>
            </motion.button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeFilter}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {filteredProjects.map((project, index) => {
              const DeviceIcon = getDeviceIcon(project.deviceType)
              
              return (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  whileHover={{ y: -4, scale: 1.02 }}
                  className="group"
                >
                  <Card className="h-full hover:shadow-soft-xl transition-all duration-300 border-border/50 bg-card/90 backdrop-blur-sm hover:border-primary/20 overflow-hidden">
                    {/* Project Image/Preview */}
                    <div className="aspect-video bg-gradient-to-br from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/20 relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      <div className="w-full h-full flex items-center justify-center p-6">
                        <div className="text-center space-y-3">
                          <div className="w-12 h-12 bg-primary/20 rounded-lg mx-auto flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            {renderIcon(DeviceIcon, "w-6 h-6 text-primary")}
                          </div>
                          <div className="space-y-1">
                            <h4 className="font-semibold text-foreground group-hover:text-primary transition-colors duration-300">
                              {project.title}
                            </h4>
                            <p className="text-xs text-muted-foreground">
                              {project.highlight}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <CardHeader className="pb-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <CardTitle className="text-lg font-bold group-hover:text-primary transition-colors duration-300">
                            {project.title}
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            <Badge className={cn("text-xs font-medium", getStatusColor(project.status))}>
                              {project.status}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {project.category}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <CardDescription className="text-sm leading-relaxed">
                        {project.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      {/* Technologies */}
                      <div className="flex flex-wrap gap-1.5">
                        {project.technologies.slice(0, 4).map((tech) => {
                          const TechIcon = techIcons[tech]
                          return (
                            <Badge
                              key={tech}
                              variant="secondary"
                              className="text-xs font-medium bg-primary/10 text-primary border-primary/20 flex items-center gap-1"
                            >
                              {TechIcon && renderIcon(TechIcon, "w-3 h-3")}
                              {tech}
                            </Badge>
                          )
                        })}
                        {project.technologies.length > 4 && (
                          <Badge variant="outline" className="text-xs">
                            +{project.technologies.length - 4}
                          </Badge>
                        )}
                      </div>

                      {/* Project Stats */}
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Users className="w-3 h-3" />
                          {project.teamSize}
                        </div>
                        <div className="flex items-center gap-1">
                          <Zap className="w-3 h-3" />
                          {project.duration}
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-2 pt-2">
                        <CardButton
                          onClick={() => window.open(project.liveUrl, '_blank')}
                          className="flex-1 text-xs px-3 py-1.5"
                          disabled={project.liveUrl === "#"}
                        >
                          <ExternalLink className="w-3 h-3 mr-1" />
                          Live Demo
                        </CardButton>
                        <CardButton
                          onClick={() => window.open(project.githubUrl, '_blank')}
                          className="flex-1 text-xs px-3 py-1.5"
                          variant="outline"
                        >
                          <Github className="w-3 h-3 mr-1" />
                          Code
                        </CardButton>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </motion.div>
        </AnimatePresence>
      </motion.div>
    </Section>
  )
}
