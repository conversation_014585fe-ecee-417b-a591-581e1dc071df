import { useState, useRef, useLayoutEffect } from 'react';
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion';
import { Lightbulb } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { BaseSectionProps, staggerContainerVariants, itemVariants } from './types';

const timelineData = [
  {
    date: '2021',
    title: 'The Beginning',
    category: 'Foundation',
    description: 'Started my Computer Engineering degree during the pandemic. Despite having no laptop and slow internet, I passed all my subjects through determination.',
    keyLearning: 'Resilience and resourcefulness are crucial for overcoming obstacles.',
  },
  {
    date: '2022',
    title: 'Discovering a Passion',
    category: 'Discovery',
    description: 'With the start of face-to-face classes, I discovered my passion for programming. I used university labs to learn HTML, CSS, & JavaScript and built my first website.',
    keyLearning: 'The moment I built something for the web, I knew this was what I wanted to do.',
  },
  {
    date: '2023',
    title: 'A Turning Point',
    category: 'Growth',
    description: 'Getting my own laptop changed everything. I could finally code anytime, leading to many late nights spent learning and building small projects.',
    keyLearning: 'Having the right tools is a catalyst for rapid growth and continuous learning.',
  },
  {
    date: '2024',
    title: 'Real-World Experience',
    category: 'Internship',
    description: "As an intern on a hospital's IT team, I worked on real websites and learned how technology helps people, bridging the gap between theory and practice.",
    keyLearning: 'Applying skills in a professional environment is the key to understanding real-world impact.',
  },
  {
    date: '2025',
    title: 'Ready for Impact',
    category: 'Career',
    description: 'After graduating with a B.S. in Computer Engineering, I am now focused on building performant web applications and seeking a full-stack developer role.',
    keyLearning: 'Eager to apply my skills to solve business problems and begin my professional career.',
  },
];

export default function JourneyTimelineSection({ className }: BaseSectionProps) {
  const [activeTimelineItem, setActiveTimelineItem] = useState<number | null>(null);
  const ref = useRef<HTMLDivElement>(null);
  const [containerHeight, setContainerHeight] = useState(0);

  useLayoutEffect(() => {
    if (ref.current) {
      const height = ref.current.offsetHeight;
      setContainerHeight(height);
      console.log('Container height updated:', height);
    }
  }, [activeTimelineItem]);

  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start center', 'end center'],
  });

  const avatarY = useTransform(
    scrollYProgress,
    [0, 1],
    [0, Math.max(containerHeight - 48, 0)]
  );

  const traceHeight = useTransform(scrollYProgress, [0, 1], [0, containerHeight]);

  return (
    <section className={cn('py-24 sm:py-32', className)}>
      {/* Section Header */}
      <div className="mb-20 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-2"
        >
          <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
            A TIMELINE OF MY GROWTH
          </span>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight"
        >
          My{' '}
          <span className="relative font-apparel font-black italic -skew-x-12 inline-block bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift [filter:drop-shadow(0_0_15px_rgba(148,56,213,0.5))_drop-shadow(0_0_1.5px_rgba(0,0,0,0.2))]">
            Journey
          </span>
        </motion.h2>
      </div>

      <div className="relative max-w-3xl mx-auto">
        {/* Timeline Track */}
        <div className="absolute left-12 top-0 bottom-0 z-10">
          {/* Background track - thicker with subtle glow */}
          <div className="w-2 h-full bg-neutral-800 rounded-full shadow-[inset_0_0_10px_rgba(0,0,0,0.5)]"></div>
          {/* Animated progress track - thicker with inner glow */}
          <motion.div
            className="absolute top-0 left-0 w-2 rounded-full bg-gradient-to-b from-purple-500 via-pink-500 to-orange-500 shadow-[inset_0_0_15px_rgba(255,255,255,0.3),_0_0_20px_rgba(147,51,234,0.4),_0_0_40px_rgba(147,51,234,0.2)]"
            style={{ height: traceHeight }}
          />
        </div>
        
        {/* Avatar that moves along the timeline - positioned independently */}
        <motion.div
          className="absolute top-0 z-20"
          style={{
            y: avatarY,
            // Position at left-12 (3rem) + half track width (0.25rem) - half avatar width (1.5rem)
            // Updated for the thicker 2px (0.5rem) track
            left: 'calc(3rem + 0.25rem - 1.5rem)'
          }}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="relative w-12 h-12">
            <img
              src="/image/profile.png"
              alt="CJ Jutba's avatar"
              className="w-full h-full rounded-full object-cover shadow-xl ring-2 ring-purple-500/20"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                const fallback = target.nextElementSibling as HTMLElement;
                if (fallback) fallback.style.display = 'flex';
              }}
            />
            <div
              className="absolute inset-0 w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 shadow-xl ring-2 ring-purple-500/20 flex items-center justify-center text-white font-bold text-sm hidden"
              style={{ display: 'none' }}
            >
              CJ
            </div>
          </div>
        </motion.div>

        {/* Timeline Content */}
        <motion.div
          ref={ref}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={staggerContainerVariants}
          className="space-y-24 ml-24 md:ml-28 relative z-0"
        >
          {timelineData.map((item, index) => (
            <motion.div key={index} variants={itemVariants} className="relative">
              <Card
                className={cn(
                  'flex-1 cursor-pointer transition-all duration-300 transform hover:scale-[1.02]',
                  'bg-neutral-900/50 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] rounded-2xl border-transparent',
                  activeTimelineItem === index && 'ring-2 ring-primary/50'
                )}
                onClick={() => setActiveTimelineItem(activeTimelineItem === index ? null : index)}
              >
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-xl font-bold">{item.title}</CardTitle>
                      <CardDescription className="text-primary font-medium mt-1">
                        {item.date}
                      </CardDescription>
                    </div>
                    <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                      {item.category}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed mb-4">
                    {item.description}
                  </p>
                  <AnimatePresence>
                    {activeTimelineItem === index && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        className="border-t border-border/50 pt-4 mt-4"
                      >
                        <div className="space-y-3">
                          <div className="flex items-center gap-2 text-sm font-medium text-primary">
                            <Lightbulb className="w-4 h-4" />
                            Key Takeaway
                          </div>
                          <p className="text-sm text-muted-foreground italic">
                            "{item.keyLearning}"
                          </p>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}